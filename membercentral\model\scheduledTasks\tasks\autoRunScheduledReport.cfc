<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.runReportResult = autoRunScheduledReport()>
		
		<cfif NOT local.runReportResult.success>
			<cfthrow message="Error running autoRunScheduledReport()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.runReportResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="autoRunScheduledReport" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<!--- get the next report to run --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="rpt_getNextScheduledReport">
				<cfprocresult name="local.qryScheduledReport" resultset="1">
			</cfstoredproc>

			<cfif local.qryScheduledReport.recordCount>
				<!--- convert to struct for easier referencing --->
				<cfset local.strReport = QueryRowData(local.qryScheduledReport,1)>

				<cfset runScheduledReport(strReport=local.strReport)>

				<cfset markScheduledReportAsFinished(itemID=local.strReport.itemID, isSuccess=true)>

				<cfset local.returnStruct.itemCount++>
			</cfif>
		<cfcatch type="Any">
			<cfset markScheduledReportAsFinished(itemID=local.strReport.itemID, isSuccess=false)>

			<cfset application.objError.sendError(cfcatch=cfcatch, customMessage='Error running scheduled report #local.strReport.itemID#', objectToDump=local.strReport)>

			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="runScheduledReport" access="public" output="false" returntype="void">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.sendReportEmail = false>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.strReport.siteCode)>
		
		<cfset local.reportRunResult = createObject("component","model.admin.#arguments.strReport.toolCFC#").autoRunReport(
			reportUID=arguments.strReport.reportUID, 
			reportAction=arguments.strReport.reportAction, 
			runByMemberID=0, 
			filename=arguments.strReport.filename)>

		<cfif NOT local.reportRunResult.success>
			<cfset local.sendReportEmail = true>
			<cfset local.emailBody = "<b>NOTE: We were unable to run the scheduled report #arguments.strReport.reportName#.</b><br/>You will need to run this report manually from Control Panel.<br/>The most common reason of this error is that the report uses member field sets that someone has changed without updating the report that relies on them. Running the report manually resets the report for future scheduled sends.">
			<cfset local.arrMailAttachments = []>
		<cfelse>
			<cfset local.emailBody = arguments.strReport.emailBody>
			<cfset local.objReportHelper = CreateObject("component","model.admin.reports.report")>

			<cfswitch expression="#arguments.strReport.reportAction#">
				<cfcase value="customcsv">
					<cfif local.reportRunResult.csvRowCount>
						<cfset local.reportDocResult = local.objReportHelper.generateReportDocument(reportID=arguments.strReport.reportID, reportTitle=arguments.strReport.reportName,
							siteID=local.mc_siteinfo.siteID, orgCode=local.mc_siteinfo.orgCode, siteCode=arguments.strReport.siteCode, directoryPath=local.reportRunResult.folderPath, 
							reportFileName=arguments.strReport.filename, memberID=local.mc_siteInfo.sysMemberID, buttonText="Report")>
						<cfif local.reportDocResult.success>
							<cfset local.emailBody = local.emailBody & "<br/><br/>" & local.reportDocResult.downloadHTML>
						<cfelse>
							<cfset local.emailBody = "<b>NOTE: We were able to run the scheduled report #arguments.strReport.reportName#, but unable to link to the resulting file in this email.</b><br/>You will need to run this report manually from Control Panel.<br/>Contact us if you continue to see this message.">
						</cfif>
						<cfset local.sendReportEmail = true>
					</cfif>
				</cfcase>
				<cfcase value="pdf">
					<cfif NOT local.reportRunResult.isReportEmpty>
						<cfset local.reportDocResult = local.objReportHelper.generateReportDocument(reportID=arguments.strReport.reportID, reportTitle=arguments.strReport.reportName,
							siteID=local.mc_siteinfo.siteID, orgCode=local.mc_siteinfo.orgCode, siteCode=arguments.strReport.siteCode, directoryPath=local.reportRunResult.folderPath, 
							reportFileName=arguments.strReport.filename, memberID=local.mc_siteInfo.sysMemberID, buttonText="Report")>
						<cfif local.reportDocResult.success>
							<cfset local.emailBody = local.emailBody & "<br/><br/>" & local.reportDocResult.downloadHTML>
						<cfelse>
							<cfset local.emailBody = "<b>NOTE: We were able to run the scheduled report #arguments.strReport.reportName#, but unable to link to the resulting file in this email.</b><br/>You will need to run this report manually from Control Panel.<br/>Contact us if you continue to see this message.">
						</cfif>
						<cfset local.sendReportEmail = true>
					</cfif>
				</cfcase>
			</cfswitch>
		</cfif>

		<cfif local.sendReportEmail AND len(arguments.strReport.toEmail)>
			<cfsavecontent variable="local.emailContent">
				<cfoutput>
				#local.emailBody#
				<br/>
				<hr/>
				This email was generated from the scheduled report #arguments.strReport.reportName# at #datetimeformat(now(),"m/d/yy h:nn tt")# CT.
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(arguments.strReport.toEmail,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>

			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=local.mc_siteInfo.orgName, email=local.mc_siteInfo.networkEmailFrom },
				emailto=local.arrEmailTo,
				emailreplyto="",
				emailsubject="Report Ready: #arguments.strReport.reportName#",
				emailtitle=arguments.strReport.reportName,
				emailhtmlcontent=local.emailContent,
				siteID=local.mc_siteinfo.siteID,
				memberID=local.mc_siteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
				sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
			)>
		</cfif>
	</cffunction>

	<cffunction name="markScheduledReportAsFinished" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="isSuccess" type="boolean" required="true">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="rpt_markScheduledReportAsFinished">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="#arguments.isSuccess#">
		</cfstoredproc>
	</cffunction>

</cfcomponent>