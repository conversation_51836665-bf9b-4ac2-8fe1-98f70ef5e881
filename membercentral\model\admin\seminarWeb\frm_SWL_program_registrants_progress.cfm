<cfsavecontent variable="local.progressJS">
	<cfoutput>
	<script language="javascript">
		function toggleSWActivateExam(rid){
			var activate = $('.response' + rid + ' a.toggleSWActivateExam').data('activate');
			var toggleText = activate?'reactivate':'deactivate';
			var toggleResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					setToggleSWActivateExamButton(!activate, rid);
				} else {
					alert('We were unable to '+ toggleText +' this response.');
				}
			};
			let toggleButton = $('.response' + rid + ' a.toggleSWActivateExam');
			mca_initConfirmButton(toggleButton, function(){
				var objParams = { enrollmentID:#local.enrollmentID#, responseID:rid, isActive:activate };
				TS_AJX('ADMINSWCOMMON','toggleActivateExam',objParams,toggleResult,toggleResult,10000,toggleResult);
			},'<i class="fa-solid fa-shuffle"></i>',null,'Toggling...');
		}
		function setToggleSWActivateExamButton(activate, rid){	
			$('.response' + rid +' a.toggleSWActivateExam').data('activate',activate);
			$('.response' + rid +' a.toggleSWActivateExam').html('<i class="fa-solid fa-shuffle"></i>');
			$('.response' + rid +' a.toggleSWActivateExam').removeClass('disabled');
			$('.response' + rid +' a.toggleSWActivateExam').prop('title',(activate?'Activate':'Deactivate') +' this response.').tooltip('dispose').tooltip();
			if(activate){
				$('.response' + rid).addClass('table-warning');
				$('.response' + rid + ' .d-flex').append('<span class="badge badge-warning ml-auto">Inactive</span>');
			}
			else {
				$('.response' + rid).removeClass('table-warning');
				$('.response'+rid+' .badge.badge-warning.ml-auto').remove();
			}
		}
		$(function() { 
			mca_initNavPills('SWLProgressTabs'); 
			top.$('##MCModalLabel').html('Manage Progress for #encodeForJavaScript(local.qryEnrollment.firstname)# #encodeForJavaScript(local.qryEnrollment.lastname)# ');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.progressJS)#">

<cfoutput>
<div class="p-2">
	<div class="p-1">
		<span>Seminar: <strong>#encodeForHTML(local.qryEnrollment.seminarName)#</strong></span>
	</div>

	<ul class="nav nav-pills nav-pills-dotted mt-2" id="SWLProgressTabs">
		<li class="nav-item">
			<a class="nav-link" id="responsesTab" data-toggle="pill" href="##pills-responsesTab" role="tab"
				aria-controls="pills-responsesTab" aria-selected="false" data-tabname="responses">Form Responses</a>
		</li>
	</ul>

	<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
		<div class="tab-pane fade" id="pills-responsesTab" role="tabpanel" aria-labelledby="responsesTab">
			<cfloop query="local.qrySeminarForms">
				<cfquery name="local.qryFormDetailsIndiv" dbtype="query">
					select *
					from [local].qryFormDetails
					where depomemberdataid = #local.qryEnrollment.DepomemberDataID#
					and seminarFormID = #local.qrySeminarForms.seminarFormID#
					order by dateDelivered DESC, responseID DESC
				</cfquery>
				<cfset local.thisFormPassingPct = local.qrySeminarForms.passingPct>
				<div class="divFrom#local.qrySeminarForms.seminarFormID#" style="margin-bottom:20px;">
					<div><b>#local.qrySeminarForms.formTitle#</b></div>
					<div>#local.qrySeminarForms.loadpoint#</div><br/>
					<cfif local.qryFormDetailsIndiv.recordcount>
						<table class="table table-sm table-striped table-bordered">
							<thead>
							<tr>
								<th width="20%">Response ID</th>
								<th width="20%">Time Started</th>
								<th width="20%">Time Completed</th>
								<th>Score</th>
								<th width="15%">Tools</th>
							</tr>
							</thead>
							<tbody>
							<cfloop query="local.qryFormDetailsIndiv">
								<tr class="response#local.qryFormDetailsIndiv.responseid# <cfif local.qryFormDetailsIndiv.isActive is 0>table-warning</cfif>">
									<td class="align-top">
										<div class="d-flex">
											<span>#local.qryFormDetailsIndiv.responseid#</span>
											<cfif local.qryFormDetailsIndiv.isActive is 0>
												<span class="badge badge-warning ml-auto">Inactive</span>
											</cfif>
										</div>
									</td>
									<td class="align-top">
										#dateformat(local.qryFormDetailsIndiv.datedelivered,"m/d/yy")# #timeformat(local.qryFormDetailsIndiv.datedelivered,"h:mm tt")#
									</td>
									<td class="align-top dateCompleted text-nowrap">
										<cfif len(local.qryFormDetailsIndiv.datecompleted)>
											#dateformat(local.qryFormDetailsIndiv.datecompleted,"m/d/yy")# #timeformat(local.qryFormDetailsIndiv.datecompleted,"h:mm tt")#
										<cfelse>
											Not submitted
										</cfif>
									</td>
									<cfif local.qryFormDetailsIndiv.formTypeID is 2>
										<td class="align-top text-nowrap">
											#val(local.qryFormDetailsIndiv.passingPct)#% (#local.qryFormDetailsIndiv.correctCount#/#local.qryFormDetailsIndiv.questionCount#) 
											<cfif local.qryFormDetailsIndiv.passingPct gte local.thisFormPassingPct> PASS <cfelse> FAIL </cfif>
										</td>
									<cfelse>
										<td class="align-top">Not applicable</td>
									</cfif>	
									<td class="align-top responseAction">
										<cfif local.qryFormDetailsIndiv.isActive is 0>
											<a class="toggleSWActivateExam btn btn-xs btn-outline-primary p-1" data-activate="1" data-confirm="0" title="Activate this Response" href="javascript:toggleSWActivateExam(#local.qryFormDetailsIndiv.responseid#);"><i class="fa-solid fa-shuffle"></i></a>
										<cfelse>
											<a class="toggleSWActivateExam btn btn-xs btn-outline-primary p-1" data-activate="0" data-confirm="0" title="Inactivate this Response" href="javascript:toggleSWActivateExam(#local.qryFormDetailsIndiv.responseid#);"><i class="fa-solid fa-shuffle"></i></a>
										</cfif>
									</td>
								</tr>
							</cfloop>
							</tbody>
						</table>
					<cfelse>
						<div><i>No responses found.</i></div>
					</cfif>
				</div>
			</cfloop>
		</div>
	</div>
</div>	
</cfoutput>