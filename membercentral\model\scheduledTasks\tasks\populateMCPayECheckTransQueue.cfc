<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="600">

		<cfset local.result = populateQueue()>
		<cfif NOT local.result.success>
			<cfthrow message="Error running populateQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.result.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="populateQueue" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		
		<cftry>
			<cfquery name="local.qryPopulateQueue" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpMCPayECheckProfiles') IS NOT NULL 
						DROP TABLE ##tmpMCPayECheckProfiles;
					IF OBJECT_ID('tempdb..##tmpEarliestMCPayECheckClosedBatches') IS NOT NULL 
						DROP TABLE ##tmpEarliestMCPayECheckClosedBatches;
					IF OBJECT_ID('tempdb..##tmpMCPayECheckProfilesForQueue') IS NOT NULL 
						DROP TABLE ##tmpMCPayECheckProfilesForQueue;
					CREATE TABLE ##tmpMCPayECheckProfiles (MPProfileID int);
					CREATE TABLE ##tmpEarliestMCPayECheckClosedBatches (MPProfileID int, earliestDepositDate datetime);
					CREATE TABLE ##tmpMCPayECheckProfilesForQueue (MPProfileID int, filterOptions varchar(30));

					DECLARE @PreviousDayStart datetime, @PreviousDayEnd datetime, @today datetime, @statusReady int, 
						@itemCount int, @closedStatusID int;

					SET @today = CAST(GETDATE() AS date); 
					SET @PreviousDayStart = DATEADD(DAY, -1, @today);
					SET @PreviousDayEnd = DATEADD(MILLISECOND, -3, @today);
				
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='addMCPayECheckTrans', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

					SELECT @closedStatusID = statusID 
					FROM dbo.tr_batchStatuses 
					WHERE [status] = 'Closed';

					INSERT INTO ##tmpMCPayECheckProfiles (MPProfileID)
					SELECT DISTINCT mp.profileID
					FROM dbo.mp_profiles AS mp
					INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
					WHERE g.isActive = 1
					AND g.gatewayType = 'MCPayECheck';

					INSERT INTO ##tmpMCPayECheckProfilesForQueue (MPProfileID, filterOptions)
					SELECT DISTINCT mp.MPProfileID, tmp.listitem
					FROM ##tmpMCPayECheckProfiles AS mp
					CROSS APPLY dbo.fn_varcharListToTable('achbatch,achreturn',',') as tmp;

					INSERT INTO ##tmpEarliestMCPayECheckClosedBatches (MPProfileID, earliestDepositDate)
					SELECT mp.MPProfileID, MIN(b.depositDate)
					FROM dbo.tr_batches AS b
					INNER JOIN ##tmpMCPayECheckProfiles AS mp ON mp.MPProfileID = b.payProfileID
					WHERE b.statusID = @closedStatusID
					AND b.depositDate <= @PreviousDayEnd
					GROUP BY mp.MPProfileID;

					BEGIN TRAN;
						WITH MCPayECheckFinalData AS (
							SELECT mp.MPProfileID, mp.filterOptions, b.earliestDepositDate AS dateFrom,
								DATEADD(MILLISECOND, -3, DATEADD(DAY, 1, b.earliestDepositDate)) AS dateTo
							FROM ##tmpMCPayECheckProfilesForQueue AS mp
							INNER JOIN ##tmpEarliestMCPayECheckClosedBatches AS b ON b.MPProfileID = mp.MPProfileID
							WHERE b.earliestDepositDate <= @PreviousDayEnd
								UNION ALL
							SELECT MPProfileID, filterOptions, DATEADD(DAY, 1, dateFrom) AS dateFrom,
								DATEADD(DAY, 1, dateTo) AS dateTo
							FROM MCPayECheckFinalData
							WHERE DATEADD(DAY, 1, dateTo) <= @PreviousDayEnd
						)
						INSERT INTO platformQueue.dbo.queue_addMCPayECheckTrans (MPProfileID, dateFrom, dateTo, filterOptions, statusID, dateAdded, dateupdated)
						SELECT mp.MPProfileID, mp.dateFrom, mp.dateTo, mp.filterOptions, @statusReady, GETDATE(), GETDATE()
						FROM MCPayECheckFinalData AS mp
						LEFT OUTER JOIN platformQueue.dbo.queue_addMCPayECheckTrans AS qid ON qid.MPProfileID = mp.MPProfileID
							AND qid.filterOptions = mp.filterOptions
						WHERE qid.itemID IS NULL
						ORDER BY mp.MPProfileID, mp.filterOptions, mp.dateFrom, mp.dateTo;

						SET @itemCount = @@ROWCOUNT;

						-- resume task
						IF @itemCount > 0
							EXEC dbo.sched_resumeTask @name='Process MCPayEcheck Transactions Queue', @engine='MCLuceeLinux';
					COMMIT TRAN;

					SELECT @itemCount AS itemCount;

					IF OBJECT_ID('tempdb..##tmpMCPayECheckProfiles') IS NOT NULL 
						DROP TABLE ##tmpMCPayECheckProfiles;
					IF OBJECT_ID('tempdb..##tmpEarliestMCPayECheckClosedBatches') IS NOT NULL 
						DROP TABLE ##tmpEarliestMCPayECheckClosedBatches;
					IF OBJECT_ID('tempdb..##tmpMCPayECheckProfilesForQueue') IS NOT NULL 
						DROP TABLE ##tmpMCPayECheckProfilesForQueue;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.returnStruct.itemCount = val(local.qryPopulateQueue.itemCount)>
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>