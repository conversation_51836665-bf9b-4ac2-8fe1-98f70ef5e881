ALTER PROC dbo.rpt_createDashboardFromTemplate_Financial
@siteID int,
@dashboardName varchar(200), 
@dashboardDesc varchar(300),
@categoryName varchar(100),
@createdByMemberID int, 
@dashboardID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @categoryID int;
	declare @tblDashDataFinancial TABLE (objectTypeID int, useRevenueGLFilter bit, useInvoiceProfileFilter bit);

	/*
	Object types when site has batches: 
		ALLOCSUMBYGLBYMONTH, ALLOCSUMBYGATETYPE, ALLOCSUMBYGLBYYEAR, ALLOCSUMTOPMEMBER, ALLOCSUMTOPFIRM, 
		PAYMENTSUMBYGATETYPE, PAYMENTRECORDERSUM, PAYMENTRECORDER
	Object types when site has invoices: 
		ARBYMONTH, ARBYINVPROFBYFY, INVCREATEDBYYEAR
	*/

	insert into @tblDashDataFinancial (objectTypeID, useRevenueGLFilter, useInvoiceProfileFilter)
	select distinct dot.objectTypeID, dot.useRevenueGLFilter, dot.useInvoiceProfileFilter
	from dbo.sites as s
	inner join dbo.tr_batches as b on b.orgID = s.orgID and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
	cross join dbo.rpt_dashboardObjectTypes as dot
	where s.siteID = @siteID
	and dot.objectTypeCode in ('ALLOCSUMBYGLBYMONTH','ALLOCSUMBYGATETYPE','ALLOCSUMBYGLBYYEAR','ALLOCSUMTOPMEMBER',
		'ALLOCSUMTOPFIRM','PAYMENTSUMBYGATETYPE','PAYMENTRECORDERSUM','PAYMENTRECORDER')
	and dot.isActive = 1
	and isnull(dot.siteID,@siteID) = @siteID
		union all
	select distinct dot.objectTypeID, dot.useRevenueGLFilter, dot.useInvoiceProfileFilter
	from dbo.sites as s
	inner join dbo.tr_invoices as i on i.orgID = s.orgID
	cross join dbo.rpt_dashboardObjectTypes as dot
	where s.siteID = @siteID
	and dot.objectTypeCode in ('ARBYMONTH','ARBYINVPROFBYFY','INVCREATEDBYYEAR')
	and isnull(dot.siteID,@siteID) = @siteID
	and dot.isActive = 1;

	IF @@ROWCOUNT > 0 BEGIN

		EXEC dbo.rpt_createDashboard @siteID=@siteID, @dashboardName=@dashboardName, @dashboardDesc=@dashboardDesc, 
			@dashboardCode='FINANCIAL', @createdByMemberID=@createdByMemberID, @dashboardID=@dashboardID OUTPUT;

		-- get category created in the createDashboard call and change its name
		select @categoryID = c.categoryID
		from dbo.rpt_dashboards as d
		inner join dbo.cms_categories as c on c.categoryTreeID = d.categoryTreeID
		where d.dashboardID = @dashboardID;

		update dbo.cms_categories 
		set categoryName = @categoryName
		where categoryID = @categoryID;

		-- add the objects
		INSERT INTO dbo.rpt_dashboardObjects (dashboardID, objectLabel, objectDesc, categoryID, objectTypeID, visualTypeID,
			objectOrder, objectDataXML, createdByMemberID, dateCreated)
		SELECT @dashboardID, null, null, @categoryID, tmp.objectTypeID, dot.defaultVisualTypeID, 1, 
			case when tmp.useRevenueGLFilter = 1 or tmp.useInvoiceProfileFilter = 1 then '<obj></obj>' else null end, @createdByMemberID, getdate()
		FROM @tblDashDataFinancial AS tmp
		INNER JOIN dbo.rpt_dashboardObjectTypes AS dot ON dot.objectTypeID = tmp.objectTypeID;

		EXEC dbo.rpt_reorderDashboardObjects @categoryID=@categoryID;

		INSERT INTO platformStatsMC.dbo.rpt_dashboardObjectsCache (objectID, xmlDataset, lastUpdated, nextUpdate, timeMS)
		select do.objectID, '<data/>', dateadd(HOUR,-8,getdate()), getdate(), 0
		from dbo.rpt_dashboardObjects as do
		where do.dashboardID = @dashboardID;

	END ELSE BEGIN
		SET @dashboardID = 0;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
