<cfcomponent output="no">

	<!--- GL Functions --->
	<cffunction name="getGLAccountByAccountCode" access="public" output="no" returntype="numeric">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="accountCode" type="string" required="yes">

		<cfset var glAccountID = 0>
		
		<cfstoredproc procedure="tr_getGLAccountByAccountCode" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgid#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountcode#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="glAccountID">
		</cfstoredproc>
	
		<cfreturn glAccountID>
	</cffunction>
	
	<!--- Invoices --->
	<cffunction name="createInvoice" access="public" output="no" returntype="struct">
		<cfargument name="invoiceProfileID" type="numeric" required="yes">
		<cfargument name="assignedToMemberID" type="numeric" required="yes">
		<cfargument name="dateBilled" type="date" required="yes">
		<cfargument name="dateDue" type="date" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="tr_createInvoice" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.invoiceProfileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.assignedToMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.assignedToMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.dateBilled#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.dateDue#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.invoiceID">
				<cfprocparam type="Out" cfsqltype="CF_SQL_VARCHAR" variable="local.invoiceNumber">
			</cfstoredproc>
			<cfset local.strReturn = { rc=0, invoiceid=local.invoiceid, invoicenumber=local.invoiceNumber }>
		<cfcatch>
			<cfset local.strReturn = { rc=-1, invoiceid=0, invoicenumber='' }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="closeInvoice" access="public" output="no" returntype="struct">
		<cfargument name="invoiceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		
		<cfquery name="local.qryGetInvoice" datasource="#application.dsn.membercentral.dsn#">
			select m.activeMemberID, i.orgID
			from dbo.tr_invoices as i
			inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
			where i.invoiceID = <cfqueryparam value="#arguments.invoiceID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cftry>
			<cfif local.qryGetInvoice.recordcount>
				<cfstoredproc procedure="tr_closeInvoice" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGetInvoice.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGetInvoice.activeMemberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invoiceID#">
				</cfstoredproc>
			<cfelse>
				<cfthrow message="Invalid InvoiceID">
			</cfif>
			<cfset local.strReturn.rc = 0>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn.rc = -1>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<!--- sales and tax --->
	<cffunction name="recordSale" access="public" output="no" returntype="struct">
		<cfargument name="ownedByOrgID" type="numeric" required="yes">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="assignedToMemberID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="detail" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="creditGLAccountID" type="numeric" required="yes">
		<cfargument name="invoiceID" type="numeric" required="yes">
		<cfargument name="parentTransactionID" type="numeric" required="no" default="0">
		<cfargument name="stateIDForTax" type="numeric" required="no" default="0">
		<cfargument name="zipForTax" type="string" required="no" default="">
		<cfargument name="bypassTax" type="boolean" required="no" default="0">
		<cfargument name="recogStart" type="date" required="no" default="#now()#">
		<cfargument name="recogEnd" type="date" required="no" default="#now()#">

		<cfset var local = structNew()>
		
		<cftry>
			<!--- validate: recogStart cannot be before transactionDate. recogEnd must be on/later than recogStart --->
			<cfif DateCompare(arguments.transactionDate,arguments.recogStart,"d") is 1>
				<cfthrow message="Recognition Date cannot be after the Transaction Date.">				
			</cfif>
			<cfif DateCompare(arguments.recogStart,arguments.recogEnd,"d") is 1>
				<cfthrow message="Recognition Start Date cannot be after the Recognition End Date.">				
			</cfif>

			<!--- create xmlSchedule based on recognition dates. spread amount evenly over date range by month --->
			<cfset local.arrDeferredSchedule = arrayNew(1)>
			<cfset local.thisMonth = arguments.recogStart>
			<cfloop condition="#local.thisMonth# lte #arguments.recogEnd#">
				<cfset local.tmpStr = { amt=0, dt=dateformat(local.thisMonth,"m/d/yyyy") }>
				<cfset arrayAppend(local.arrDeferredSchedule,local.tmpStr)>
				<cfset local.thisMonth = dateAdd("m",1,local.thisMonth)>
			</cfloop>
			<cfset local.schRowAmount = NumberFormat(arguments.amount / arrayLen(local.arrDeferredSchedule),"9.99")>
			<cfloop array="#local.arrDeferredSchedule#" index="local.thisRow">
				<cfset local.thisRow.amt = local.schRowAmount>	
			</cfloop>
			<cfset local.schRowSum = NumberFormat(local.schRowAmount * arrayLen(local.arrDeferredSchedule),"9.99")>
			<cfif local.schRowSum neq NumberFormat(arguments.amount,"9.99")>
				<cfset local.arrDeferredSchedule[1].amt = NumberFormat(local.arrDeferredSchedule[1].amt + (arguments.amount-local.schRowSum),"9.99")>
			</cfif>
			<cfset local.xmlSchedule = CreateObject("component","model.admin.transactions.transactionAdmin").convertDeferredScheduledArrayToXML(arrSchedule=local.arrDeferredSchedule)>

			<cfif val(arguments.stateIDForTax) and len(arguments.zipForTax)>
				<cfset local.strTaxIndiv = getTaxForUncommittedSale(saleGLAccountID=arguments.creditGLAccountID, 
					saleAmount=arguments.amount, transactionDate=arguments.transactionDate, stateIDForTax=arguments.stateIDForTax,
					zipForTax=arguments.zipForTax)>
			</cfif>

			<cfstoredproc procedure="tr_createTransaction_sale" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ownedByOrgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.assignedToMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Active">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.detail,500)#">
				<cfif val(arguments.parentTransactionID)>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.parentTransactionID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.creditGLAccountID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.invoiceID#">
				<cfif val(arguments.stateIDForTax)>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.stateIDForTax#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
				<cfif len(arguments.zipForTax)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.zipForTax#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
				<cfif isDefined("local.strTaxIndiv.totalTaxAmt")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.strTaxIndiv.totalTaxAmt#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#int(val(arguments.bypassTax))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#ToString(local.xmlSchedule)#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.transactionID">
			</cfstoredproc>
			<cfquery name="local.qrySaleWithTax" datasource="#application.dsn.membercentral.dsn#">
				select paymentDueAmount
				from dbo.fn_tr_salesWithAmountDue(<cfqueryparam value="#arguments.assignedToMemberID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.ownedByOrgID#" cfsqltype="CF_SQL_INTEGER">)
				where transactionID = <cfqueryparam value="#local.transactionID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset local.strReturn = { rc=0, transactionID=local.transactionID, amountWithTax=val(local.qrySaleWithTax.paymentDueAmount) }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn = { rc=-1, transactionID=0, amountWithTax=0 }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getTaxForUncommittedSale" access="public" output="no" returntype="struct">
		<cfargument name="saleGLAccountID" type="numeric" required="yes">
		<cfargument name="saleAmount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipForTax" type="string" required="yes">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTaxProvider">
			select stp.profileID, stpr.providerName, stp.orgID, stp.apiKey, tj.taxCode
			from dbo.tr_glAccounts as gl
			inner join dbo.tr_salesTaxProfiles as stp on stp.profileID = gl.salesTaxProfileID
			inner join dbo.tr_salesTaxProviders as stpr on stpr.providerID = stp.providerID
			left outer join dbo.tr_salesTaxCategories_TaxJar as tj on tj.categoryID = gl.salesTaxTaxJarCategoryID
			where gl.glAccountID = <cfqueryparam value="#arguments.saleGLAccountID#" cfsqltype="CF_SQL_INTEGER">
			and stp.status = 'A'
		</cfquery>

		<cfif local.qryTaxProvider.providerName eq "TaxJar">

			<cfset local.qryStates = application.objCommon.getStates()>
			<cfquery name="local.qryState" dbtype="query">
				select stateCode
				from [local].qryStates
				where stateID = <cfqueryparam value="#arguments.stateIDForTax#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset local.strTemp = { orgID=local.qryTaxProvider.orgID, apiKey=local.qryTaxProvider.apiKey, toState=local.qryState.stateCode, 
									 toZIP=left(arguments.zipForTax,5), taxCode=local.qryTaxProvider.taxCode, saleAmount=arguments.saleAmount }>
			<cfset local.strTax = createObject("component","model.system.platform.salesTax.TaxJar").getTaxForUncommittedSale(argumentcollection=local.strTemp)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTax">
				select 1 as row, ta.taxAuthorityID, ta.GLAccountID as taxGLAccountID, ta.AuthorityName, null as taxRuleID, 
					#val(local.strTax.taxrate)# as taxRate, #val(local.strTax.tax)# as taxAmount
				from dbo.tr_taxAuthorities as ta
				where ta.salesTaxProfileID = #local.qryTaxProvider.profileID#
			</cfquery>
			<cfset local.strReturn = { totalTaxAmt=val(local.strTax.tax), qryTax=local.qryTax }>

		<cfelse>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTax">
				select row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount 
				from dbo.fn_tr_getTaxForUncommittedSale(<cfqueryparam value="#arguments.saleGLAccountID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.saleAmount#" cfsqltype="CF_SQL_DECIMAL" scale="2">,<cfqueryparam value="#arguments.transactionDate#" cfsqltype="CF_SQL_DATE">,<cfqueryparam value="#arguments.stateIDForTax#" cfsqltype="CF_SQL_INTEGER">)
				order by row
			</cfquery>
			<cfquery name="local.qryTaxTotal" dbtype="query">
				select sum(taxAmount) as totalAmt
				from [local].qryTax
			</cfquery>
			<cfset local.strReturn = { totalTaxAmt=val(local.qryTaxTotal.totalAmt), qryTax=local.qryTax }>

		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<!--- batches and payments --->
	<cffunction name="getBatchID" access="public" output="no" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="GLAccountID" type="numeric" required="yes">
		<cfargument name="batchDate" type="date" required="yes">
		<cfargument name="responseReasonCode" type="string" required="yes">
		<cfargument name="paystatus" type="string" required="yes">
		<cfargument name="createdByMemberID" type="numeric" required="yes">
		<cfargument name="paymentMethod" type="string" required="no" default="">
		<cfargument name="externalBatchID" type="string" required="no" default="">
		<cfargument name="mode" type="string" required="no" default="">
		
		<cfset var local = structNew()>
		<cfset local.batchid = 0>

		<cftry>
			<cfquery name="local.qryProfile" datasource="#application.dsn.membercentral.dsn#">
				select p.gatewayID, s.orgID, p.profileID, p.profileCode
				from dbo.mp_profiles as p
				inner join dbo.sites as s on s.siteID = p.siteID
				where p.profileCode = <cfqueryparam value="#arguments.profileCode#" cfsqltype="CF_SQL_VARCHAR">
				and p.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
				and p.status = 'A'
			</cfquery>
			<cfif local.qryProfile.recordcount is 0>
				<cfreturn local.batchid>
			</cfif>
			
			<cfif listFind("1,2",local.qryProfile.gatewayID) and arguments.paystatus eq "Pending">
				<cfquery name="local.qryFindBatch" datasource="#application.dsn.membercentral.dsn#">
					select batchID
					from dbo.tr_batches
					where orgID = <cfqueryparam value="#local.qryProfile.orgID#" cfsqltype="CF_SQL_INTEGER">
					and statusID = 1
					and batchCode = 'PENDINGPAYMENTS'
					and isSystemCreated = 1
				</cfquery>
				<cfset local.batchID = val(local.qryFindBatch.batchID)>
			</cfif>
	
			<cfif local.batchID is 0>
						
				<!--- find batch to put it on. if not there, auto create it --->
				<cfset local.strGLAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=arguments.GLAccountID, orgID=local.qryProfile.orgID)>
				
				<!--- Bank Draft --->
				<cfif local.qryProfile.gatewayID EQ 16>
					<cfset local.batchCode = "#arguments.responseReasonCode#_#local.qryProfile.profileID#_#arguments.GLAccountID#">
					<cfset local.batchName = "#arguments.responseReasonCode# #local.qryProfile.profileCode# #local.strGLAccount.qryAccount.accountName#">
					<cfset local.isSystemCreated = 0>
				<cfelse>
					<cfset local.batchCode = "#dateformat(arguments.batchDate,"YYYYMMDD")#_#local.qryProfile.profileID#_#arguments.GLAccountID#">
					<cfset local.batchName = "#dateformat(arguments.batchDate,"YYYYMMDD")# #local.qryProfile.profileCode# #local.strGLAccount.qryAccount.accountName#">
					<cfset local.isSystemCreated = 1>
				</cfif>

				<cftry>
					<cfquery name="local.qryFindOrCreateBatch" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						DECLARE @orgID int, @batchID int, @payProfileID int, @batchCode varchar(40), @batchName varchar(400), 
							@depositDate datetime, @isSystemCreated bit, @createdByMemberID int, @batchTypeID int;

						SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProfile.orgID#">;
						SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProfile.profileID#">;
						SET @batchCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.batchCode#">;
						SET @batchName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.batchName#">;
						SET @depositDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.batchDate#">;
						SET @isSystemCreated = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.isSystemCreated#">;
						SET @createdByMemberID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.createdByMemberID#">,0);
						SET @batchTypeID = 1;
						
						<!--- BankDraft --->
						<cfif local.qryProfile.gatewayID EQ 16>
							select @batchID = max(batchID)
							from dbo.tr_batches
							where orgID = @orgID
							and batchTypeID = @batchTypeID
							and batchCode = @batchCode
							and statusID = 1;
						
						<!--- MCPayECheck --->
						<cfelseif local.qryProfile.gatewayID EQ 19>
							<!--- Deposit Day 9:14 PM --->
							DECLARE @depositDayBatchEndDate datetime;
							SET @depositDayBatchEndDate = DATETIMEFROMPARTS(YEAR(@depositDate),MONTH(@depositDate),DAY(@depositDate),21,14,59,997);

							IF GETDATE() > @depositDayBatchEndDate BEGIN
								SET @depositDate = DATEADD(DAY, 1, @depositDate);
								SET @batchCode = CONVERT(varchar(8), @depositDate, 112) + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="_#local.qryProfile.profileID#_#arguments.GLAccountID#">;
								SET @batchName = CONVERT(varchar(8), @depositDate, 112) + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value=" #local.qryProfile.profileCode# #local.strGLAccount.qryAccount.accountName#">;
							END

							select @batchID = max(batchID)
							from dbo.tr_batches
							where orgID = @orgID
							and batchTypeID = @batchTypeID
							and batchCode = @batchCode
							and statusID = 1
							and isSystemCreated = 1;
						<cfelse>
							select @batchID = batchID
							from dbo.tr_batches
							where orgID = @orgID
							and batchTypeID = @batchTypeID
							and batchCode = @batchCode
							and statusID = 1
							and isSystemCreated = 1;
						</cfif>

						IF @batchID IS NULL
							EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@payProfileID, @batchTypeID=@batchTypeID, @batchCode=@batchCode, 
								@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, @isSystemCreated=@isSystemCreated, 
								@createdByMemberID=@createdByMemberID, @batchID=@batchID OUTPUT;

						SELECT @batchID AS batchID;
					</cfquery>

					<cfset local.batchid = val(local.qryFindOrCreateBatch.batchID)>
				<cfcatch type="Any">
					<cfset local.tmpCatch = { type="", message="The batch could not be created.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
					<cfset local.tmpErr = { batchCode=local.batchCode, batchName=left(local.batchName,400) }>
					<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
					<cfreturn local.batchid>
				</cfcatch>
				</cftry>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.batchID = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.batchid>
	</cffunction>
	
	<cffunction name="recordPayment" access="public" output="no" returntype="struct">
		<cfargument name="ownedByOrgID" type="numeric" required="yes">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="assignedToMemberID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="detail" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="debitGLAccountID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="historyID" type="numeric" required="yes">
		<cfargument name="batchID" type="numeric" required="yes">
		<cfargument name="offeredPaymentFee" type="boolean" required="no" default="0" hint="offered processing fees while making payment">
		<cfargument name="isApplePay" type="boolean" required="no" default="0">
		<cfargument name="isGooglePay" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryProfileID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 profileID
			FROM dbo.mp_profiles
			WHERE profileCode = <cfqueryparam value="#arguments.profileCode#" cfsqltype="CF_SQL_VARCHAR">
			AND siteID = <cfqueryparam value="#arguments.recordedOnSiteID#" cfsqltype="CF_SQL_INTEGER">
			AND status = 'A'
		</cfquery>

		<cftry>
			<cfquery name="local.qryCreatePaymentTransaction" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @transactionID int;

				EXEC dbo.tr_createTransaction_payment @ownedByOrgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ownedByOrgID#">,
					@recordedOnSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">, 
					@assignedToMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.assignedToMemberID#">,
					@recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">,
					@statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">,
					@status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">,
					@detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.detail,500)#">,
					@amount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">,
					@transactionDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">,
					@debitGLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.debitGLAccountID#">,
					@profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryProfileID.profileid)#">,
					@historyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.historyID#">,
					@batchid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.batchID#">,
					@offeredPaymentFee = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.offeredPaymentFee#">,
					@isApplePay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isApplePay#">,
					@isGooglePay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isGooglePay#">,
					@transactionID = @transactionID OUTPUT;

				SELECT @transactionID AS transactionID;
			</cfquery>
			<cfset local.strReturn = { rc=0, transactionID=local.qryCreatePaymentTransaction.transactionID }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { rc=-1, transactionID=0 }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="recordRefund" access="public" output="no" returntype="struct">
		<cfargument name="ownedByOrgID" type="numeric" required="yes">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="detail" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="creditGLAccountID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="historyID" type="numeric" required="yes">
		<cfargument name="batchID" type="numeric" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryProfileID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 profileID
			FROM dbo.mp_profiles
			WHERE profileCode = <cfqueryparam value="#arguments.profileCode#" cfsqltype="CF_SQL_VARCHAR">
			AND siteID = <cfqueryparam value="#arguments.recordedOnSiteID#" cfsqltype="CF_SQL_INTEGER">
			AND status = 'A'
		</cfquery>

		<cftry>
			<cfstoredproc procedure="tr_createTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ownedByOrgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.detail,500)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.creditGLAccountID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryProfileID.profileid)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.historyID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.transactionID">
			</cfstoredproc>
			<cfset local.strReturn = { rc=0, transactionID=local.transactionID }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { rc=-1, transactionID=0 }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="recordVoid" access="public" output="no" returntype="struct">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<!--- had to do this in a cfquery because the proc wont return xml as a longvarchar. --->
			<cfquery name="local.qryVoid" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @recordedOnSiteID int, @recordedByMemberID int, @statsSessionID int, @transactionID int, @tids xml, @vidPool xml;
				SET @recordedOnSiteID = <cfqueryparam value="#arguments.recordedOnSiteID#" cfsqltype="CF_SQL_INTEGER">;
				SET @recordedByMemberID = <cfqueryparam value="#arguments.recordedByMemberID#" cfsqltype="CF_SQL_INTEGER">;
				SET @statsSessionID = <cfqueryparam value="#arguments.statsSessionID#" cfsqltype="CF_SQL_INTEGER">;
				SET @transactionID = <cfqueryparam value="#arguments.transactionID#" cfsqltype="CF_SQL_INTEGER">;

				EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @transactionID=@transactionID, @checkInBounds=1, @vidPool=@vidPool OUTPUT, 
						@tids=@tids OUTPUT;

				SELECT @vidPool as vidPool, @tids as tids;
			</cfquery>
			<cfset local.strReturn = { rc=0, vidPool=local.qryVoid.vidPool, tids=local.qryVoid.tids }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { rc=-1, vidPool='', tids='' }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="recordAllocation" access="public" output="no" returntype="struct">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="saleTransactionID" type="numeric" required="yes">
		<cfargument name="ovBatchID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="tr_createTransaction_allocation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.saleTransactionID#">
				<cfif arguments.ovBatchID gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ovBatchID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.transactionID">
			</cfstoredproc>
			<cfset local.strReturn = { rc=0, transactionID=local.transactionID }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { rc=-1, transactionID=0 }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="allocateToInvoice" access="public" output="no" returntype="struct">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="invoiceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		
		<cftry>
			<cfstoredproc procedure="tr_allocateToInvoice" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.invoiceID#">
			</cfstoredproc>
			<cfset local.strReturn.rc = 0>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn.rc = -1>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="allocateToSale" access="public" output="no" returntype="struct">
		<cfargument name="recordedOnSiteID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="date" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="saleTransactionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		
		<cftry>
			<cfstoredproc procedure="tr_allocateToSale" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedOnSiteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.transactionDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.saleTransactionID#">
			</cfstoredproc>
			<cfset local.strReturn.rc = 0>
		<cfcatch type="Any">
			<cfset local.strReturn.rc = -1>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="recordAdditionalPaymentFees" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="assignedToMemberID" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		<cfargument name="statsSessionID" type="numeric" required="true">
		<cfargument name="paymentTransactionID" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="qryAdditionalFees" type="query" required="true">
		<cfargument name="paymentFeeTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":false, "errmsg":"" }>
		
		<cftry>
			<cfquery name="local.qryRecordAdditionalPaymentFees" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @invoiceProfileID int, @invoiceID int, @assignedToMemberID int, @transactionDate datetime, 
						@invoiceNumber varchar(19), @recordedByMemberID int, @statsSessionID int, @detail varchar(1000), @amount decimal(18,2), 
						@taxAmount decimal(18,2), @GLAccountID int, @transactionID int, @trTypeID int, @paymentTID int, @totalAmtDue decimal(18,2) = 0,
						@stateIDforTax int, @zipForTax varchar(25), @paymentFeeTypeID tinyint;
				
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					SET @GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#">;
					SET @assignedToMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.assignedToMemberID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">;
					SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#">;
					SET @paymentTID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">;
					SET @paymentFeeTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentFeeTypeID#">;
					SET @transactionDate = GETDATE();

					SELECT @invoiceProfileID = profileID
					FROM dbo.fn_tr_getInvoiceProfileForGL(@GLAccountID);

					SELECT @trTypeID = dbo.fn_tr_getRelationshipTypeID('PaymentFeeTrans');
					
					BEGIN TRAN;
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@assignedToMemberID,
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transactionDate, @dateDue=@transactionDate,
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						<cfloop query="arguments.qryAdditionalFees">
							SELECT @detail = NULL, @amount = NULL, @transactionID = NULL;

							SET @detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.qryAdditionalFees.additionalFeesRevTransDesc#">;
							SET @stateIDForTax = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryAdditionalFees.stateIDForTax#">;
							SET @zipForTax = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.qryAdditionalFees.zipForTax#">;
							SET @amount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.qryAdditionalFees.additionalFeesExcTax#">;
							SET @taxAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.qryAdditionalFees.additionalFeesTax#">;
							SET @totalAmtDue = @totalAmtDue + @amount + @taxAmount;

							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID,
								@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@recordedByMemberID,
								@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=NULL,
								@amount=@amount, @transactionDate=@transactionDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
								@stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0,
								@bypassAccrual=0, @xmlSchedule=NULL, @transactionID=@transactionID OUTPUT;

							-- designate as optional processing fee donation revenue (paymentFeeTypeID = 1) OR surcharge (paymentFeeTypeID = 2)
							UPDATE dbo.tr_transactionSales
							SET paymentFeeTypeID = @paymentFeeTypeID
							WHERE transactionID = @transactionID
							AND orgID = @orgID;

							-- relationship between processing fee and payment
							INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
							VALUES (@trTypeID, @transactionID, @paymentTID, @orgID);
						</cfloop>

						EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;

						EXEC dbo.tr_allocateToInvoice @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
							@statsSessionID=@statsSessionID, @amount=@totalAmtDue, @transactionDate=@transactionDate, 
							@paymentTransactionID=@paymentTID, @invoiceID=@invoiceID;
					COMMIT TRAN;

					SELECT @invoiceID AS invoiceID;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.strReturn = { "success":true, "invoiceID":local.qryRecordAdditionalPaymentFees.invoiceID, "errmsg":"" }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { "success":false, "errmsg":cfcatch.message }>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>