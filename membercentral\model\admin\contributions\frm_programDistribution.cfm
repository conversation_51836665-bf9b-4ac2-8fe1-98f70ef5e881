<cfsavecontent variable="local.programDistribJS">
	<cfoutput>
		#local.strGLAcctWidget.js#
	<script language="javascript">
		function validateDistributionForm() {
			var arrReq = new Array();
			mca_hideAlert('err_programdistrib');
			var distribPct = $('##distPct').val();

			if($('##distDesc').val().trim().length == 0) arrReq[arrReq.length] = 'Enter Distribution Description.';
			if($('##distCode').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the Distribution Code.';
			if($('##GLAccountID').val() == 0 || $.trim($('##GLAccountID').val()).length == 0) arrReq[arrReq.length] = 'Select a GL Account.';
			if (parseInt($('##distPct').val()) < 0 || parseInt($('##distPct').val()) > 100) arrReq[arrReq.length] = 'Enter a valid Distribution Percentage.';
			if($('##distUID').length && $('##distUID').val().trim().length == 0) arrReq[arrReq.length] = 'Enter Distribution UID.';

			if(arrReq.length) {
				mca_showAlert('err_programdistrib', arrReq.join('<br/>'));
				return false;
			}

			top.$("##btnMCModalSave").prop('disabled',true);
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.programDistribJS)#">
<cfoutput>
<div id="divProgDistribForm" class="p-3">
	<form name="frmProgDistrib" id="frmProgDistrib" action="#local.formlink#" method="post" onsubmit="return validateDistributionForm()">
		<input type="hidden" name="programID" id="programID" value="#local.programID#">
		<input type="hidden" name="distribID" id="distribID" value="#local.distribID#">

		<button type="submit" class="d-none">Save</button>

		<div id="err_programdistrib" class="alert alert-danger mb-2 d-none"></div>

		<div class="alert alert-warning">
		Changes to Program Distributions will only affect <b>future contributions</b>. Existing Contributions will <b>not</b>  be updated to reflect changes to Program Distributions.
		</div>
		<div class="form-label-group">
			<textarea name="distDesc" id="distDesc" rows="3" class="form-control" maxlength="200">#arguments.event.getValue('distDesc')#</textarea>

			<label for="distDesc">Distribution Description *</label>
		</div>

		<div class="form-label-group">
			<input type="text" name="distCode" id="distCode" value="#replace(arguments.event.getValue('distCode'),chr(34),'&quot;','ALL')#" class="form-control" maxlength="20">

			<label for="distCode">Distribution Code *</label>
			<div class="form-text small text-dim">(used to label this distribution in reports)</div>
		</div>

		<div class="form-group">
			#local.strGLAcctWidget.html#
		</div>

		<div class="form-label-group">
			<input type="text" name="distPct" id="distPct" value="#arguments.event.getValue('distPct')#" class="form-control" maxlength="3">

			<label for="distPct">Distribution Percentage</label>
		</div>

		<cfif local.distribID gt 0 and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<div class="form-label-group">
				<input type="text" name="distUID" id="distUID" value="#arguments.event.getValue('distUID')#" class="form-control" maxlength="50">
				<label for="distUID">API ID *</label>
			</div>
		<cfelseif local.distribID gt 0>
			<div class="form-label-group">
				<input type="text" name="distUIDRO" id="distUIDRO" value="#arguments.event.getValue('distUID')#" class="form-control" maxlength="50" readonly="true">
				<label for="distUIDRO">API ID</label>
			</div>
		</cfif>
	</form>
</div>
</cfoutput>