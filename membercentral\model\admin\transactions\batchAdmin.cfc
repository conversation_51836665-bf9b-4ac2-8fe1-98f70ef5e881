<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		// build quick links -------------------------------------------------------------------------- ::
		this.link.message = buildCurrentLink(arguments.event,"message") & "&mode=direct";
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.edit = buildCurrentLink(arguments.event,"editBatch") & "&mode=direct";
		this.link.save = buildCurrentLink(arguments.event,"saveBatch") & "&mode=direct";
		this.link.view = buildCurrentLink(arguments.event,"viewBatch");
		this.link.viewCSVExport = buildCurrentLink(arguments.event,"viewBatch") & "&csv=1&mode=stream";
		this.link.viewIIFExport = buildCurrentLink(arguments.event,"viewBatch") & "&iif=1&mode=stream";
		this.link.viewACHExport = buildCurrentLink(arguments.event,"viewBatch") & "&ach=1&mode=stream";
		this.link.viewPDFExport = buildCurrentLink(arguments.event,"viewBatch") & "&pdf=1&mode=stream";
		this.link.moveBatchTransaction = buildCurrentLink(arguments.event,"moveBatchTransaction") & "&mode=direct";
		
		// method to run ------------------------------------------------------------------------------ ::
		local.methodToRun 	= this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it.
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
		
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.resultsList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=batchJSON&meth=getBatchesList&mode=stream&srID=#this.siteResourceID#";	
		arguments.event.paramValue('statusID','1,2,3');
		arguments.event.paramValue('batchName','');
		arguments.event.paramValue('depdateStart','');
		arguments.event.paramValue('depdateEnd','');

		local.urlString = "";
		local.urlString = local.urlString & '&statusID=' & arguments.event.getTrimValue('statusID');
		</cfscript>

		<cfquery name="local.qryStatus" datasource="#application.dsn.membercentral.dsn#">
			select statusID, status
			from dbo.tr_batchStatuses
			order by statusID
		</cfquery>
		<cfquery name="local.qryPayProfiles" datasource="#application.dsn.membercentral.dsn#">
			SELECT distinct mp.profileID, mp.profileName
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.tr_batches as b on b.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#"> 
				and b.payProfileID = mp.profileID
			where mp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			ORDER BY mp.profileName
		</cfquery>

		<cfquery name="local.qryTypes" datasource="#application.dsn.membercentral.dsn#">
			select batchTypeID, batchType
			from dbo.tr_batchTypes
			<cfif NOT arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
				where batchType <> 'Deferred Revenue Recognitions'
			</cfif>
			order by batchType
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listBatches.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="moveBatchTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('BatchAdmin') & "&mca_ta=saveMoveBatchTransaction&mode=stream";

		arguments.event.setValue('tid',int(val(arguments.event.getTrimValue('tid',0))));
		arguments.event.setValue('bid',int(val(arguments.event.getTrimValue('bid',0))));
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchMoveTransaction') is not 1>
			<cflocation url="#this.link.message#&ec=MBTNP" addtoken="no">
		</cfif>

		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

			select t.transactionID, b.batchID, tp.profileID, b.batchName, b.depositDate, 
				case when t.typeID = 8 then 'VOID of ' + t.detail else t.detail end as detail, 
				t.amount, 
				m2.lastname 
				+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
				+ ', ' + m2.firstname 
				+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
				+ ' (' + m2.membernumber + ')' as memberName
			from dbo.tr_transactions as t
			inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
			inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchId
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberiD = m.activeMemberID
			inner join dbo.organizations as o on o.orgID = @orgID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('tid')#">
			and b.batchID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('bid')#">;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=MBTNF" addtoken="no">
		</cfif>
		
		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_getOpenBatches">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.profileID#">
			<cfprocresult name="local.qryOpenBatches">
		</cfstoredproc>
		<cfif local.qryOpenBatches.recordcount is 0>
			<cflocation url="#this.link.message#&ec=MBTNOB" addtoken="no">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_moveBatchTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveMoveBatchTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.setValue('tid',int(val(arguments.event.getValue('tid',0))))>
		<cfset arguments.event.setValue('bid',int(val(arguments.event.getValue('bid',0))))>
		<cfset arguments.event.setValue('newbid',int(val(arguments.event.getValue('newbid',0))))>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchMoveTransaction') is not 1>
			<cflocation url="#this.link.message#&ec=MBTNP" addtoken="no">
		</cfif>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_moveBatchTransaction">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('tid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('bid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('newbid')#">
			</cfstoredproc>
			<cfset local.moved = true>
		<cfcatch type="any">
			<cfset local.moved = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfif local.moved>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.closeMoveBT();
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cflocation url="#this.link.message#&ec=SMBT" addtoken="no">
		</cfif>
	</cffunction>
	
	<cffunction name="editBatch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('BatchAdmin') & "&mca_ta=saveBatch&mode=stream";
		</cfscript>

		<cfquery name="local.qryBatch" datasource="#application.dsn.membercentral.dsn#">
			select b.batchID, b.statusID, bs.status, b.batchName, b.controlAmt, b.controlCount, b.depositDate, mp.profileName, g.gatewayType
			from dbo.tr_batches as b
			inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
			left outer join dbo.mp_profiles as mp 
				inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				on mp.profileID = b.payProfileID
			where b.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
			and b.batchID = <cfqueryparam value="#val(arguments.event.getValue('bid',0))#" cfsqltype="CF_SQL_INTEGER">
			and b.isSystemCreated = 0
			and bs.status <> 'Posted'
		</cfquery>
		<cfif local.qryBatch.recordcount is 0 and val(arguments.event.getValue('bid',0)) gt 0>
			<cflocation url="#this.link.message#&ec=EBNF" addtoken="no">
		<cfelseif local.qryBatch.recordcount is 0 and val(arguments.event.getValue('bid',0)) is 0 and arguments.event.getValue('mc_admintoolInfo.myRights.batchCreate') is not 1>
			<cflocation url="#this.link.message#&ec=CBNP" addtoken="no">
		<cfelseif local.qryBatch.recordcount is 1 and arguments.event.getValue('mc_admintoolInfo.myRights.batchEdit') is not 1>
			<cfif NOT ( arguments.event.getValue('mc_admintoolInfo.myRights.batchClose') is 1 OR 
					arguments.event.getValue('mc_admintoolInfo.myRights.batchOpen') is 1 OR 
					arguments.event.getValue('mc_admintoolInfo.myRights.batchPost') is 1 ) >
				<cflocation url="#this.link.message#&ec=EBNP" addtoken="no">
			</cfif>
		</cfif>
		<cfif local.qryBatch.recordcount is 1>
			<cfquery name="local.qryBatchActual" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">,
					@batchID int = <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpAllBatches') IS NOT NULL 
					DROP TABLE ##tmpAllBatches;
				CREATE TABLE ##tmpAllBatches (batchID int PRIMARY KEY, actualCount int, actualAmount decimal(18,2));

				INSERT INTO ##tmpAllBatches (batchID, actualCount, actualAmount)
				VALUES (@batchID, 0, 0);

				EXEC dbo.tr_getBatchActualBulk @orgID=@orgID;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select actualCount, actualAmount
				from ##tmpAllBatches;

				IF OBJECT_ID('tempdb..##tmpAllBatches') IS NOT NULL 
					DROP TABLE ##tmpAllBatches;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>
		
		<cfif val(local.qryBatch.batchID) is 0>
			<cfquery name="local.qryPayProfiles" datasource="#application.dsn.membercentral.dsn#">
				SELECT distinct mp.profileID, mp.profileName
				FROM dbo.mp_profiles AS mp
				INNER JOIN dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
				where mp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				and mp.status IN ('A','I')
				and mg.isActive = 1
				and (mp.allowRefunds = 1 or mp.allowPayments = 1)
				and mg.gatewayID in (1,2)
				ORDER BY mp.profileName
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editBatch.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveBatch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.err = "">

		<!--- init --->
		<cfset arguments.event.setValue('bid',int(val(arguments.event.getValue('bid',0))))>
		<cfset arguments.event.setValue('statusID',int(val(arguments.event.getValue('statusID',0))))>

		<cftry>

			<!--- get batch --->
			<cfquery name="local.qryBatch" datasource="#application.dsn.membercentral.dsn#">
				select b.batchID, b.statusID, bs.status, b.batchCode, b.batchName, b.controlAmt, b.controlCount, b.depositDate, mp.gatewayID
				from dbo.tr_batches as b
				inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
				inner join dbo.mp_profiles as mp on mp.profileID = b.payProfileID
				where b.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
				and b.batchID = <cfqueryparam value="#arguments.event.getValue('bid')#" cfsqltype="CF_SQL_INTEGER">
				and b.isSystemCreated = 0
				and bs.status <> 'Posted'
			</cfquery>
		
			<cfif local.qryBatch.recordcount is 1>
				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchEdit') is not 1>
					<cfif NOT ( arguments.event.getValue('mc_admintoolInfo.myRights.batchClose') is 1 OR 
							arguments.event.getValue('mc_admintoolInfo.myRights.batchOpen') is 1 OR 
							arguments.event.getValue('mc_admintoolInfo.myRights.batchPost') is 1 ) >
						<cflocation url="#this.link.message#&ec=EBNP" addtoken="no">
					</cfif>
				</cfif>

				<!--- if we are posting a batch, run special code --->
				<cfif local.qryBatch.statusID is not 4 and arguments.event.getTrimValue('statusID') is 4>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_postBatch">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
						<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBatch.batchID#">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					</cfstoredproc>

				<cfelseif listFindNoCase("Open,Open for Modification",local.qryBatch.status)>
					<cfset arguments.event.setValue('depositDate',dateformat(arguments.event.getTrimValue('depositDate',local.qryBatch.depositDate),"m/d/yyyy"))>
					<cfset arguments.event.setValue('controlAmt',val(ReReplace(arguments.event.getTrimValue('controlAmt',local.qryBatch.controlAmt),'[^\-0-9\.]','','ALL')))>
					<cfset arguments.event.setValue('controlCount',val(arguments.event.getTrimValue('controlCount',local.qryBatch.controlCount)))>
					<cfset arguments.event.setValue('batchName',left(arguments.event.getTrimValue('batchName',local.qryBatch.batchName),400))>
				
					<cfquery name="local.qryBatchLookup" datasource="#application.dsn.membercentral.dsn#">
						select b.batchID
						from dbo.tr_batches as b
						where b.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
						and b.batchName = <cfqueryparam value="#arguments.event.getTrimValue('batchName')#" cfsqltype="CF_SQL_VARCHAR">
						and b.batchID <> <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
					<cfif local.qryBatchLookup.recordcount is 0>
						<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								DECLARE @batchID int = <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">;

								<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchEdit') is 1>
									DECLARE @newDepositDate date, @oldDepositDate date, @recordedByMemberID int;
									SET @newDepositDate = <cfqueryparam value="#arguments.event.getTrimValue('depositDate')#" cfsqltype="CF_SQL_DATE">;
									SET @oldDepositDate = <cfqueryparam value="#local.qryBatch.depositDate#" cfsqltype="CF_SQL_DATE">;
									SET @recordedByMemberID = <cfqueryparam value="#session.cfcuser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">;
								</cfif>

								BEGIN TRAN;
									UPDATE dbo.tr_batches
									SET 
									<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchEdit') is 1>
										depositDate = @newDepositDate,
										controlAmt = <cfqueryparam value="#arguments.event.getTrimValue('controlAmt')#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
										controlCount = <cfqueryparam value="#arguments.event.getTrimValue('controlCount')#" cfsqltype="CF_SQL_INTEGER">,
										batchName = <cfqueryparam value="#arguments.event.getTrimValue('batchName')#" cfsqltype="CF_SQL_VARCHAR">,
									</cfif>
										statusID = <cfqueryparam value="#arguments.event.getTrimValue('statusID')#" cfsqltype="CF_SQL_INTEGER">
									WHERE batchID = @batchID;

									<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchEdit') is 1>
										IF @newDepositDate <> @oldDepositDate
											INSERT INTO dbo.tr_batchDateHistory (batchID, type, updateDate, newDate, oldDate, recordedByMemberID)
											VALUES (@batchID, 'D', GETDATE(), @newDepositDate, @oldDepositDate, @recordedByMemberID);
									</cfif>
								COMMIT TRAN;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>
						
						<!--- if we are closing a batch tied to gateway 16, change batchcode and batchname to prevent it from being used again by the system or conflicting with constraints --->
						<cfif local.qryBatch.gatewayID is 16 and arguments.event.getTrimValue('statusID') is 3 and listLen(local.qryBatch.batchCode,"_") is 3>
							<cfset local.batchDateFormatted = dateformat(arguments.event.getTrimValue('depositDate'),'YYYYMMDD')>
							<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
								set nocount on

								declare @orgID int, @origbatchCode varchar(40), @origbatchName varchar(400), @batchCode varchar(40), @batchName varchar(400), @batchCheck int, @batchIDIncrement int;
								set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;
								set @origbatchCode = <cfqueryparam value="#local.batchDateFormatted#_#local.qryBatch.batchCode#" cfsqltype="CF_SQL_VARCHAR">;
								set @origbatchName = <cfqueryparam value="#local.batchDateFormatted# #arguments.event.getTrimValue('batchName')#" cfsqltype="CF_SQL_VARCHAR">;
								set @batchCode = @origbatchCode;
								set @batchName = @origbatchName;
								set @batchIDIncrement = 1;

								select @batchCheck = batchID from dbo.tr_batches where orgID = @orgID and (batchName = @batchName OR batchCode = @batchCode);
								while @batchCheck is not null begin
									set @batchIDIncrement = @batchIDIncrement + 1;
									set @batchCode = @origbatchCode + '_' + cast(@batchIDIncrement as varchar(3));
									set @batchName = @origbatchName + ' ' + cast(@batchIDIncrement as varchar(3));
									set @batchCheck = null;
									select @batchCheck = batchID from dbo.tr_batches where orgID = @orgID and (batchName = @batchName OR batchCode = @batchCode);
								end

								update dbo.tr_batches
								set batchCode = @batchCode,
									batchName = @batchName
								where batchID = <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">;
							</cfquery>
						</cfif>
						
					<cfelse>
						<cfset local.err = "A batch named <b>#arguments.event.getTrimValue('batchName')#</b> already exists. Enter a new batch name.">
					</cfif>

				<cfelse>
					<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
						update dbo.tr_batches
						set statusID = <cfqueryparam value="#arguments.event.getTrimValue('statusID')#" cfsqltype="CF_SQL_INTEGER">
						where batchID = <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>

				</cfif>

				<!--- if we changed status, record history. posting batches already did this so skip it if we are posting --->
				<cfif compare(local.qryBatch.statusID,arguments.event.getTrimValue('statusID')) and arguments.event.getTrimValue('statusID') neq 4>
					<cfif (local.qryBatch.statusID is 1 and listFind("2,3",arguments.event.getTrimValue('statusID')))
						or (local.qryBatch.statusID is 2 and listFind("1,3",arguments.event.getTrimValue('statusID')))
						or local.qryBatch.statusID is 3>
						<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
							INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (#local.qryBatch.batchID#, getdate(), #arguments.event.getTrimValue('statusID')#, #local.qryBatch.statusID#, #session.cfcuser.memberdata.memberid#)
						</cfquery>
					<cfelseif local.qryBatch.statusID is 1 and arguments.event.getTrimValue('statusID') is 4>
						<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
							set nocount on

							INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (#local.qryBatch.batchID#, getdate(), 3, 1, #session.cfcuser.memberdata.memberid#)

							INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (#local.qryBatch.batchID#, getdate(), 4, 3, #session.cfcuser.memberdata.memberid#)

							set nocount off
						</cfquery>
					<cfelseif local.qryBatch.statusID is 2 and arguments.event.getTrimValue('statusID') is 4>
						<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
							set nocount on

							INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (#local.qryBatch.batchID#, getdate(), 3, 2, #session.cfcuser.memberdata.memberid#)

							INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (#local.qryBatch.batchID#, getdate(), 4, 3, #session.cfcuser.memberdata.memberid#)

							set nocount off
						</cfquery>
					</cfif>
				</cfif>

			<cfelseif local.qryBatch.recordcount is 0 and arguments.event.getValue('bid') is 0>
				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchCreate') is not 1>
					<cflocation url="#this.link.message#&ec=CBNP" addtoken="no">
				</cfif>

				<cfset arguments.event.setValue('depositDate',dateformat(arguments.event.getTrimValue('depositDate',now()),"m/d/yyyy"))>
				<cfset arguments.event.setValue('controlAmt',val(ReReplace(arguments.event.getTrimValue('controlAmt',0),'[^\-0-9\.]','','ALL')))>
				<cfset arguments.event.setValue('controlCount',val(arguments.event.getTrimValue('controlCount',0)))>
				<cfset arguments.event.setValue('batchName',left(arguments.event.getTrimValue('batchName','New Batch #getTickCount()#'),400))>
				<cfset arguments.event.setValue('payProfileID',int(val(arguments.event.getValue('payProfileID',0))))>
				
				<cfquery name="local.qryBatchLookup" datasource="#application.dsn.membercentral.dsn#">
					select b.batchID
					from dbo.tr_batches as b
					where b.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
					and b.batchName = <cfqueryparam value="#arguments.event.getTrimValue('batchName')#" cfsqltype="CF_SQL_VARCHAR">
				</cfquery>
				<cfif local.qryBatchLookup.recordcount is 0>
					<cftry>
						<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_createBatch">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('payProfileID')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('batchName')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.event.getTrimValue('controlAmt')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('controlCount')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('depositDate')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.batchID">
						</cfstoredproc>
					<cfcatch type="Any">
						<cfset local.err = "A batch named <b>#arguments.event.getTrimValue('batchName')#</b> could not be created. Try again.">
					</cfcatch>
					</cftry>
				<cfelse>
					<cfset local.err = "A batch named <b>#arguments.event.getTrimValue('batchName')#</b> already exists. Enter a new batch name.">
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset local.err = "We were not able to save the changes to this batch.">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfif len(local.err)>
			<cflocation url="#this.link.edit#&bid=#val(local.qryBatch.batchID)#&err=#URLEncodedFormat(local.err)#" addtoken="no">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.closeEditBatch();
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
	</cffunction>

	<cffunction name="viewBatch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.memberTransationsLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		</cfscript>

		<cfquery name="local.qryBatch" datasource="#application.dsn.membercentral.dsn#">
			select b.batchID, b.batchTypeID, b.statusID, bs.status, b.batchName, b.batchCode, b.controlAmt, b.controlCount, b.depositDate, 
				b.payProfileID, g.gatewayType, b.isSystemCreated, 
				m2.firstname 
				+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
				+ ' ' + m2.lastname 
				+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end 
				+ ' (' + m2.membernumber + ')' as createdByMember
			from dbo.tr_batches as b
			inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
			left outer join dbo.ams_members as m 
				inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = m2.orgID
				on m.memberid = b.createdByMemberID
			left outer join dbo.mp_profiles as mp 
				inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				on mp.profileID = b.payProfileID
			where b.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
			and b.batchID = <cfqueryparam value="#val(arguments.event.getValue('bid',0))#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryBatch.recordcount is 0>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchDownload') is not 1 and (arguments.event.valueExists('csv') OR arguments.event.valueExists('iif') OR arguments.event.valueExists('ach'))>
			<cflocation url="#this.link.message#&ec=DBNP&mode=normal" addtoken="no">
		</cfif>

		<cfif arguments.event.valueExists('csv')>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportMode = arguments.event.getValue('rptmode','detail')>
			<cfif NOT listFindNoCase("detail,detailexpanded,gl,glsage,glgreatplains,qbol",local.reportMode)>
				<cfset local.reportMode = "detail">
			</cfif>

			<cfswitch expression="#arguments.event.getValue('rptmode','detail')#">
				<cfcase value="detail">
					<cfset local.reportFileName = "Detail.csv">
				</cfcase>
				<cfcase value="detailexpanded">
					<cfset local.reportFileName = "DetailExpanded.csv">
				</cfcase>
				<cfcase value="gl">
					<cfset local.reportFileName = "GL.csv">
				</cfcase>
				<cfcase value="glsage">
					<cfset local.reportFileName = "GL-Sage.csv">
				</cfcase>
				<cfcase value="glgreatplains">
					<cfset local.reportFileName = "GL-MicrosoftDynamicsGP.csv">
				</cfcase>
				<cfcase value="qbol">
					<cfset local.reportFileName = "JournalEntries.csv">
				</cfcase>
			</cfswitch>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_batchDetail">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBatch.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="csv">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.reportMode#">
			</cfstoredproc>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			<cfif not local.docResult>
				<cflocation url="#this.link.view#&bid=#local.qryBatch.batchID#" addtoken="no">
			</cfif>

		<cfelseif arguments.event.valueExists('iif')>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportFileName = reReplaceNoCase(local.qryBatch.batchName,"[^A-Z0-9]","","ALL") & ".IIF">

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_batchDetail">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBatch.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="iif">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
			</cfstoredproc>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			<cfif not local.docResult>
				<cflocation url="#this.link.view#&bid=#local.qryBatch.batchID#" addtoken="no">
			</cfif>

		<cfelseif arguments.event.valueExists('ach')>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportFileName = reReplaceNoCase(local.qryBatch.batchName,"[^A-Z0-9]","","ALL") & ".ACH">

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_batchDetail">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBatch.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="ach">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
			</cfstoredproc>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			<cfif not local.docResult>
				<cflocation url="#this.link.view#&bid=#local.qryBatch.batchID#" addtoken="no">
			</cfif>

		<cfelse>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_batchDetail">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBatch.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocresult name="local.qryBatchTransactions" resultset="1">
				<cfprocresult name="local.qryGLActivity" resultset="2">
				<cfprocresult name="local.qryGLActivitySum" resultset="3">
			</cfstoredproc>

			<cfquery name="local.qryBatchActual" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">,
					@batchID int = <cfqueryparam value="#local.qryBatch.batchID#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpAllBatches') IS NOT NULL 
					DROP TABLE ##tmpAllBatches;
				CREATE TABLE ##tmpAllBatches (batchID int PRIMARY KEY, actualCount int, actualAmount decimal(18,2));

				INSERT INTO ##tmpAllBatches (batchID, actualCount, actualAmount)
				VALUES (@batchID, 0, 0);

				EXEC dbo.tr_getBatchActualBulk @orgID=@orgID;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select actualCount, actualAmount
				from ##tmpAllBatches;

				IF OBJECT_ID('tempdb..##tmpAllBatches') IS NOT NULL 
					DROP TABLE ##tmpAllBatches;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryBatchHistory" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @batchID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBatch.batchID#">;

				select bsh.updateDate, 
					bstat.status + ' by ' + m2.firstname + ' ' + m2.lastname + ' (' + m2.membernumber + ')' + isnull(', ' + nullif(m2.company,''),'') as detail,
					ROW_NUMBER() OVER(ORDER BY bsh.updateDate, bsh.statusHistoryID) as row
				from dbo.tr_batchStatusHistory as bsh
				inner join dbo.tr_batchStatuses as bstat on bstat.statusID = bsh.statusID
				inner join dbo.ams_members as m on m.memberid = bsh.enteredByMemberID
				inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				where bsh.batchID = @batchID
					union all
				select bdh.updateDate,
					'Deposit Date changed from ' + convert(varchar(10),bdh.oldDate,101) + ' to ' + convert(varchar(10),bdh.newDate,101) + ' by ' + m2.firstname + ' ' + m2.lastname + ' (' + m2.membernumber + ')' + isnull(', ' + nullif(m2.company,''),'') as detail,
					ROW_NUMBER() OVER(ORDER BY bdh.updateDate, bdh.dateHistoryID) as row
				from dbo.tr_batchDateHistory as bdh
				inner join dbo.ams_members as m on m.memberid = bdh.recordedByMemberID
				inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				where bdh.batchID = @batchID
				order by updateDate, row;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.canMove = false>
			<cfif local.qryBatch.isSystemCreated is 0 and local.qryBatch.status eq "Open" and local.qryBatch.gatewayType neq "bankdraft" and arguments.event.getValue('mc_siteinfo.useBatches') is 1 and arguments.event.getValue('mc_admintoolInfo.myRights.batchMoveTransaction') is 1>
				<cfset local.canMove = true>
			</cfif>

			<cfset local.canVoidBatch = false>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_admintoolInfo.myRights.ManageAdvancedSettings',0) is 1 and local.qryBatchTransactions.recordcount>
				<cfset local.canVoidBatch = true>
			</cfif>

			<cfset local.hasGLsHavingInvalidQuickBookCols = CreateObject("component","model.admin.GLAccounts.GLAccounts").hasGLAccountsHavingInvalidQuickBookCols(orgID=arguments.event.getValue('mc_siteInfo.orgid'))>

			<cfif arguments.event.valueExists('pdf')>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfset local.reportFileName = reReplaceNoCase(local.qryBatch.batchName,"[^A-Z0-9]","","ALL") & ".pdf">

				<cfsavecontent variable="local.data">
					<cfoutput>
					<html>
					<head>
						<style type="text/css">
							<cfinclude template="/assets/admin/css/pdfstylesheet.css">
						</style>
					</head>
					<body>
						<cfinclude template="frm_viewBatch.cfm">
					</body>
					</html>
					</cfoutput>
				</cfsavecontent>

				<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
					<cfdocumentsection>
						#local.data#
					</cfdocumentsection>
					</cfoutput>
				</cfdocument>

				<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
				<cfif not local.docResult>
					<cflocation url="#this.link.view#&bid=#local.qryBatch.batchID#" addtoken="no">
				</cfif>
			<cfelse>
				<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.qryBatch.batchName })>
				
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_viewBatch.cfm">
				</cfsavecontent>
		
				<cfreturn returnAppStruct(local.data,"echo")>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="voidBatchTransactions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="batchID" type="numeric" required="yes">
		<cfargument name="voidMode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "errmsg":"" }>

		<cfset local.tmpRights = buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and structKeyExists(local.tmpRights,"ManageAdvancedSettings") and local.tmpRights.ManageAdvancedSettings is 1>
			<cfstoredproc procedure="tr_voidBatchTransactions" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.voidMode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="createOpenBatch" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="batchName" type="string" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">
		<cfargument name="depositDate" type="date" required="true">

		<cfset var local = structNew()>
		<cfset var newBatchID = 0>
		<cfset local.data = { "success":false, "newbatchid":0, "arropenbatches":[], "payprofileid":arguments.payProfileID, "errmsg":"" }>

		<cfset local.batchAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='BatchAdmin', siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.batchAdminSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cfif local.tmpRights.batchCreate IS 1>
			<cfquery name="local.qryBatchLookup" datasource="#application.dsn.membercentral.dsn#">
				select b.batchID
				from dbo.tr_batches as b
				where b.orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
				and b.batchName = <cfqueryparam value="#arguments.batchName#" cfsqltype="CF_SQL_VARCHAR">
			</cfquery>
			<cfif local.qryBatchLookup.recordcount is 0>
				<cftry>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_createBatch">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.batchName#">
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.depositDate#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="newBatchID">
					</cfstoredproc>
				<cfcatch type="Any">
					<cfset local.data.errmsg = "A batch named <b>#arguments.batchName#</b> could not be created. Try again.">
				</cfcatch>
				</cftry>
			<cfelse>
				<cfset local.data.errmsg = "A batch named <b>#arguments.batchName#</b> already exists. Enter a new batch name.">
			</cfif>
		<cfelse>
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
		</cfif>

		<cfif NOT len(local.data.errmsg) AND val(newBatchID)>
			<cfset local.data.newbatchid = newBatchID>

			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_getOpenBatches">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
				<cfprocresult name="local.qryOpenBatches">
			</cfstoredproc>

			<cfquery name="local.data.arropenbatches" dbtype="query" returntype="array">
				select batchid, depositdate, batchname, controlamt, controlcount, payprofileid,
					createdbymember, actualcount, actualamt, 0 as isselected
				from [local].qryOpenBatches
			</cfquery>

			<cfset local.data.arropenbatches.each(function(thisRow) {
				arguments.thisRow.batchname = len(arguments.thisRow.batchname) GT 90
												? "#left(arguments.thisRow.batchname,90)#..."
												: arguments.thisRow.batchname;
				arguments.thisRow.depositdate = DateFormat(arguments.thisRow.depositdate,"m/d/yyyy");
				arguments.thisRow.controlamt = Dollarformat(arguments.thisRow.controlamt);
				arguments.thisRow.actualamt = Dollarformat(arguments.thisRow.actualamt);
				arguments.thisRow.isselected = arguments.thisRow.batchid EQ newBatchID;
			})>

			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry!</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="MBTNF">That batch transaction was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: BA-MBTNF</cfcase>
						<cfcase value="MBTNOB">That batch transaction could not be moved. There are no other open batches.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: BA-MBTNOB</cfcase>
						<cfcase value="SMBT">There was an error moving the transaction to the selected batch.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: BA-SMBT</cfcase>
						<cfcase value="EBNF">That batch was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: BA-EBNF</cfcase>
						<cfcase value="CBNP">You do not have the necessary permissions to open a batch.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="EBNP">You do not have the necessary permissions to edit a batch.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="DBNP">You do not have the necessary permissions to download batch reports.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="MBTNP">You do not have the necessary permissions to move a transaction to another open batch.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>