<cfsavecontent variable="local.regDetailsJS">
	<cfoutput>
	<script language="javascript">
	let evRegScheduleTable;

	function initializeRegScheduleTable(){
		evRegScheduleTable = $('##evRegScheduleTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"ajax": { 
				"url": "#local.regSchedulesLink#",
				"type": "post",
			},
			"autoWidth": false,
			"columns": [
				{ "data": "rangename", "width": "55%", "orderable": false },
				{ "data": "startdate", "width": "15%", "orderable": false },
				{ "data": "enddate", "width": "15%", "orderable": false },
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						let renderData = '';
						if (type === 'display') {
							renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="editRegSchedule('+data.scheduleid+');return false;" title="Edit Registration Schedule"><i class="fa-solid fa-pencil"></i></a>';
							if (!data.haspackagestied) {
								renderData += '<a href="##" class="btn btn-sm btn-outline-danger px-2 m-1" id="btnDelSchedule'+data.scheduleid+'" onclick="removeRegSchedule('+data.scheduleid+');return false;" title="Remove Registration Schedule"><i class="fa-solid fa-calendar-minus"></i></a>';
							}
						}

						return type === 'display' ? renderData : data;
					},
					"width": "15%",
					"className": "text-center",
					"orderable": false
				}
			],
			"order": [[1, 'asc']],
			"searching": false,
		});
	}
	function reloadRegScheduleTable(){
		evRegScheduleTable.draw();
	}
	function validateEventRegDetailsForm() {
		var arrReq = new Array();
		mca_hideAlert(['err_ev_regdetail','err_conf_emails']);

		if ($('##regStartTime').val() == '') arrReq[arrReq.length] = 'Enter the Registration Start Time.';
		if ($('##regEndTime').val() == '') arrReq[arrReq.length] = 'Enter the Registration End Time.';

		<cfif local.strEvent.qryEventRegMeta.registrationtypeid EQ 1>
			if ($('##GLAccountID').val() == 0 || $.trim($('##GLAccountID').val()).length == 0) arrReq[arrReq.length] = 'Choose the GL Account for this event.';
			if ($("input:radio[name='regEditAllowed']:checked").val() == '1' && $('##regEditDeadline').val().length == 0) arrReq[arrReq.length] = 'Specify a deadline for registrant modifications.';
		</cfif>
		
		if ($('##replyToEmail').val() == '') arrReq[arrReq.length] = 'Enter the e-mail address to which confirmation e-mail replies will be sent.';
		
		if (arrReq.length > 0) {
			mca_showAlert('err_ev_regdetail', arrReq.join('<br/>'), true);
			return false;
		}
		
		$('.btnSaveEventRegDetails').attr('disabled', true);
		return true;
	}

	function clickOnlineMeeting(m) {
		$('##divOnlineMeeting').toggleClass('d-none', (m==1 ? false : true));
	}
	function previewOM() {
		window.open('/?#local.baseTestLink#&evAction=online&eid=#arguments.event.getValue('eid')#&mode=full&prevmode=#createUUID()#');
	}
	function chkRedURL() {
		if ($('##onlineEmbedOverrideLink').val().length > 0) {
			$('##ev_redurlmsg').html('This disables Embed Stream above, requiring you to create a web page or link to a streaming provider. If page is hosted on this Website, the Embed Code to Track Registrant must be used to track registrant time.');
			$('##ev_embedcodemsg').html('This code ONLY works on your site\'s pages. ONLY use if building a custom page for the event. This code is not required if you use Embed Stream.');
			$('.embedcode').removeClass('text-muted');
			$('##justview3').css('color','##000');
		} else {
			$('##ev_redurlmsg').html('');
			$('##ev_embedcodemsg').html('');
			$('.embedcode').addClass('text-muted');
			$('##justview3').css('color','##ddd');
		}
	}

	<!--- changing types --->
	function fnChangeToRSVP() {
		var msg = '<div class="alert alert-warning">
					<b>Are you sure you want to change this registration type to RSVP?</b><br/><br/>
					Once changed, all data tied to this previous registration type will be erased and<br/>
					will no longer appear on screen or in any reports tied to this event.
					</div>';
		MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Changing Registration to RSVP',
				iframe: false ,
				strmodalbody: { 
					content: msg
				},
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: 'fnDoChangeToRSVP',
					extrabuttonlabel: 'OK'
				}
			});
	}
	function fnChangeToFull() {
		var msg = '<div class="alert alert-warning">
					<b>Are you sure you want to change this registration type to Full Online Registration?</b><br/><br/>
					Once changed, all data tied to this previous registration type will be erased and<br/>
					will no longer appear on screen or in any reports tied to this event.
					</div>';
		MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Changing Registration to Full Online Registration',
				iframe: false ,
				strmodalbody: { 
					content: msg
				},
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: 'fnDoChangeToFull',
					extrabuttonlabel: 'OK'
				}
			});	
	}
	function fnChangeToAlt() {
		var msg = '<div class="alert alert-warning">
					<b>Are you sure you want to change this registration type to Alternate URL Registration?</b><br/><br/>
					Once changed, all data tied to this previous registration type will be erased and<br/>
					will no longer appear on screen or in any reports tied to this event.
					</div>';
		MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Changing Registration to Alternate URL Registration',
				iframe: false ,
				strmodalbody: { 
					content: msg
				},
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: 'fnDoChangeToAlt',
					extrabuttonlabel: 'OK'
				}
			});	
	}
	function fnDoChangeToRSVP() {
		$('##btnMCModalSave').prop('disabled', true);
		self.location.href = '#this.link.insertRegistration#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&registrationTypeID=2';
	}
	function fnDoChangeToFull() {
		$('##btnMCModalSave').prop('disabled', true);
		self.location.href = '#this.link.insertRegistration#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&registrationTypeID=1';
	}
	function fnDoChangeToAlt() {
		$('##btnMCModalSave').prop('disabled', true);
		self.location.href = '#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration&alturl=1';
	}
	
	function editRegSchedule(sID) {
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: sID > 0 ? 'Edit Date Range' : 'Add New Date Range',
				iframe: true,
				contenturl: '#local.regScheduleEditLink#&sID=' + sID,
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSubmit").click',
					extrabuttonlabel: 'Save',
				}
			});
	}
	function removeRegSchedule(sid) {
		var checkResult = function(r) {console.log(r);
			if (r.success && r.success.toLowerCase() == 'true'){
				doRemoveRegSchedule(sid);
			} else {
				var msg1 = 'There are rates linked with this schedule entry, and linked rate(s) may be invalid after deletion.';
				var msg2 = 'There are custom fields linked with this schedule entry, and custom fields may no longer be tied to revenue if they are not linked with this schedule entry.';
				var msg = '';
				
				if(r.ratelinkedcount > 0 && r.fieldlinkedcount > 0) msg = 'Please note.\n1. ' + msg1 + '\n' + '2. ' + msg2 + '\nAre you sure you want to delete?';
				else if(r.ratelinkedcount > 0) msg = msg1 + ' Are you sure you want to delete?';
				else if(r.fieldlinkedcount > 0) msg = msg2 + ' Are you sure you want to delete?';
				
				if(confirm(msg)) {
					doRemoveRegSchedule(sid);
				}
			}
		};
		
		let delBtnElement = $('##btnDelSchedule'+sid);
		mca_initConfirmButton(delBtnElement, function(){
			var objParams = { sID:sid };
			TS_AJX('ADMINEVENT','checkLinkedAvailableRates',objParams,checkResult,checkResult,10000,checkResult);
		});
	}
	function doRemoveRegSchedule(sid) {
		var removeRegScheduleData = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadRegScheduleTable();
				try { top.reloadRatesTable(); } catch (e){ }; /* reload the rates grid that shows schedule name */
			} else {
				alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to remove this registration schedule.');
				$('##btnDelSchedule'+sid).removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			}
		};
		
		var objParams = { scheduleID:sid, eventSRID:#this.siteResourceID# };
		TS_AJX('ADMINEVENT','deleteRegistrationSchedule',objParams,removeRegScheduleData,removeRegScheduleData,10000,removeRegScheduleData);
	}
	function hideDeadlineSetRow() {
		$('.regEditSetRow').hide();
	}
	function showDeadlineSetRow() {
		$('.regEditSetRow').show();
	}
	function getCalendarRefundPolicy() {
		var msg = '<div id="divCalRefundContent"><div class="p-2"><span class="font-weight-bold">Calendar Refund Policy:</span><textarea rows="3" id="calenderSetting" class="form-control form-control-sm mt-2">#local.strEvent.qryEventMeta.refundContent#</textarea></div></div>';
		MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			title: 'Calender Setting',
			strmodalbody: {
				content: msg
			},
			strmodalfooter : {
				classlist: 'text-left',
				showclose: true,
				showextrabutton: false,
			}
		});
	}

	$(function() {
		mca_setupDateTimePickerRangeFields('regStartTime','regEndTime',15);
		<cfif local.strEvent.qryEventRegMeta.registrationtypeid EQ 1>
			mca_setupDateTimePickerRangeFields('onlineEnterStartTime','onlineEnterEndTime',15);
			mca_setupDateTimePickerField('regEditDeadline','','',30);
			chkRedURL();
		</cfif>
		mca_setupCalendarIcons('frmEventRegDetails');
		mca_setupTagsInput(['replyToEmail', 'notifyEmail'], 'err_conf_emails', "#application.regEx.email#", 'email address');
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.regDetailsJS)#">

<cfoutput>
<form name="frmEventRegDetails" id="frmEventRegDetails" action="#this.link.saveEventRegDetails#" method="POST" onsubmit="return validateEventRegDetailsForm();">
<input type="hidden" name="cid" value="#arguments.event.getValue('cid')#">
<input type="hidden" name="eID" value="#arguments.event.getValue('eID')#">
<input type="hidden" name="registrationid" value="#arguments.event.getValue('registrationid')#">
<input type="hidden" name="registrationTypeid" value="#arguments.event.getValue('registrationTypeid')#">
<input type="hidden" name="expireContentID" value="#arguments.event.getValue('expireContentID')#">
<input type="hidden" name="registrantCapContentID" value="#arguments.event.getValue('registrantCapContentID')#">
<input type="hidden" name="expireDeadlineContentID" value="#arguments.event.getValue('expireDeadlineContentID')#">
<input type="hidden" name="registrantEditRefundContentID" value="#arguments.event.getValue('registrantEditRefundContentID')#">

<div id="err_ev_regdetail" class="alert alert-danger mb-2 d-none"></div>
<div class="form-group row">
	<label class="col-sm-3 col-form-label">Registration Type</label>
	<div class="col-sm-9 col-form-label">
		<div class="row">
			<div class="col">
				<cfif len(local.strEvent.qryEventMeta.altRegistrationURL)>
					Alt URL Registration
				<cfelseif local.strEvent.qryEventRegMeta.registrationtypeid is 1>
					Full Online Registration
				<cfelse>
					RSVP
				</cfif>
			</div>
			<div class="col-auto" id="divRegType">
				<cfif len(local.strEvent.qryEventMeta.altRegistrationURL)>
					<span>Change to <a href="javascript:fnChangeToRSVP();">RSVP</a> or <a href="javascript:fnChangeToFull();">Full Online Registration</a></span>
				<cfelseif local.strEvent.qryEventRegMeta.registrationtypeid is 1 and local.strEvent.qryEventRegMeta.activeRegistrantCount is 0>
					<span>Change to <a href="javascript:fnChangeToRSVP();">RSVP</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
				<cfelseif local.strEvent.qryEventRegMeta.registrationtypeid is 2 and local.strEvent.qryEventRegMeta.rsvpRegistrantCount is 0>
					<span>Change to <a href="javascript:fnChangeToFull();">Full Online Registration</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
				<cfelseif local.strEvent.qryEventRegMeta.activeRegistrantCount is 0 AND local.strEvent.qryEventRegMeta.rsvpRegistrantCount is 0>
					<span>Change to <a href="javascript:fnChangeToFull();">Full Online Registration</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
				</cfif>
				<cfif local.strEvent.qryEventRegMeta.activeRegistrantCount OR local.strEvent.qryEventRegMeta.rsvpRegistrantCount>
					<span class="font-italic">There are active registrants; the registration type cannot be changed.</span>
				</cfif>
			</div>
		</div>
	</div>
</div>
<div class="form-group row">
	<div class="col text-sm-right">
		<button type="submit" name="btnSaveEventRegDetails" class="btn btn-sm btn-primary btnSaveEventRegDetails">Save Details</button>
	</div>
</div>

<h5>Registration Dates</h5>

<div class="form-group row">
	<label for="regStartTime" class="col-sm-3 col-form-label-sm font-size-md">Registration Open *</label>
	<div class="col-sm-9">
		<div class="row">
			<div class="col-md col-sm-12 pr-md-0">
				<div class="input-group input-group-sm">
					<input type="text" name="regStartTime" id="regStartTime" value="#dateFormat(arguments.event.getValue('regStartTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('regStartTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="From">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="regStartTime"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</div>
			<div class="col-md-auto px-md-2 d-flex align-items-center">to</div>
			<div class="col-md col-sm-12 px-md-0">
				<div class="input-group input-group-sm">
					<input type="text" name="regEndTime" id="regEndTime" value="#dateFormat(arguments.event.getValue('regEndTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('regEndTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="To">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="regEndTime"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</div>
			<div class="col-md-auto pl-md-2 d-flex align-items-center">#local.regTimeZoneName# Time</div>
		</div>
	</div>
</div>
<div class="form-group row">
	<label for="expireContent" class="col-sm-3 col-form-label-sm font-size-md">
		Expiration Message<br/><i>(displayed when registration is not open)</i>
	</label>
	<div class="col-sm-9">
		<textarea name="expireContent" id="expireContent" rows="3" class="form-control form-control-sm">#arguments.event.getValue('expireContent')#</textarea>
	</div>
</div>

<cfif local.strEvent.qryEventRegMeta.registrationtypeid is 1>
	<h5 class="mt-5">
		Registration Schedule
		<a href="/?pg=support&sa=zenDeskGuide&link=************-How-to-use-registration-schedules-" target="_blank" class="ml-2">
			<i class="fa-solid fa-circle-question"></i>
		</a>
	</h5>
	<div class="form-group row">
		<div class="col">
			Date ranges control pricing availability for rates, custom fields, and tickets.
		</div>
		<div class="col-auto">
			<span><a href="javascript:editRegSchedule(0)"><i class="fa-regular fa-circle-plus"></i> Add Date Range</a></span>
		</div>
	</div>
	<div class="form-group row mt-2">
		<div class="col">
			<table id="evRegScheduleTable" class="table table-striped table-sm table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Name of Date Range</th>
						<th>Start Date</th>
						<th>End Date</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
</cfif>

<h5 class="mt-5">Confirmation E-mails</h5>
<div id="err_conf_emails" class="alert alert-danger mb-2 d-none"></div>
<div class="form-group row">
	<label for="replyToEmail" class="col-sm-4 col-form-label-sm font-size-md">
		Replies Sent To *<br/><i>(one or more e-mail addresses)</i>
	</label>
	<div class="col-sm-8">
		<input type="text" id="replyToEmail" name="replyToEmail" value="#replace(arguments.event.getValue('replyToEmail'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
	</div>
</div>
<div class="form-group row">
	<label for="notifyEmail" class="col-sm-4 col-form-label-sm font-size-md">
		Confirmations CC'd To<br/><i>(one or more e-mail addresses)</i>
	</label>
	<div class="col-sm-8">
		<input type="text" id="notifyEmail" name="notifyEmail" value="#replace(arguments.event.getValue('notifyEmail'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
	</div>
</div>

<h5 class="mt-5">Registration Limit</h5>
<div class="form-group row">
	<label for="registrantCap" class="col-sm-4 col-form-label-sm font-size-md">Registrant Cap</label>
	<div class="col-sm-8">
		<input type="text" name="registrantCap" value="#arguments.event.getValue('registrantCap')#" class="form-control form-control-sm">
	</div>
</div>
<div class="form-group row">
	<label for="registrantCapContent" class="col-sm-4 col-form-label-sm font-size-md">
		Registrant Cap Message<br/><i>(displays when cap is reached)</i>
	</label>
	<div class="col-sm-8">
		<textarea name="registrantCapContent" rows="3" class="form-control form-control-sm">#arguments.event.getValue('registrantCapContent')#</textarea>
	</div>
</div>

<cfif local.strEvent.qryEventRegMeta.registrationtypeid EQ 1>
	<h5 class="mt-5">Registration Modifications by Registrants</h5>
	<div class="mt-1">Settings to control the ability for registrants to edit their registrations on your site.</div>
	<div class="form-group row mt-2">
		<label class="col-sm-4 col-form-label">Allow Modifications *</label>
		<div class="col-sm-8">
			<div class="form-check">
				<input type="radio" name="regEditAllowed" id="regEditAllowed_0" value="0" class="form-check-input" onclick="hideDeadlineSetRow();" <cfif arguments.event.getValue('regEditAllowed',0) is 0>checked</cfif>>
				<label class="form-check-label" for="regEditAllowed_0">No - Registrants may not modify registration</label>
			</div>
			<div class="form-check">
				<input type="radio" name="regEditAllowed" id="regEditAllowed_1" value="1" class="form-check-input" onclick="showDeadlineSetRow();" <cfif arguments.event.getValue('regEditAllowed',0) is 1>checked</cfif>>
				<label class="form-check-label" for="regEditAllowed_1">Yes - Registrants may modify registration before the specified deadline</label>
			</div>
		</div>
	</div>
	<div class="form-group row regEditSetRow" <cfif arguments.event.getValue('regEditAllowed',0) is 0>style="display:none;"</cfif>>
		<label for="regEditDeadline" class="col-sm-4 col-form-label-sm font-size-md">Modification Deadline *</label>
		<div class="col-sm-8">
			<div class="input-group input-group-sm">
				<input type="text" name="regEditDeadline" id="regEditDeadline" value="<cfif len(arguments.event.getValue('regEditDeadline'))>#dateFormat(arguments.event.getValue('regEditDeadline'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('regEditDeadline'),'h:mm tt')#</cfif>" class="form-control form-control-sm dateControl">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="regEditDeadline"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<span class="pl-1 d-flex align-items-center">#local.regTimeZoneName# Time</span>
			</div>
		</div>
	</div>
	<div class="form-group row regEditSetRow" <cfif arguments.event.getValue('regEditAllowed',0) is 0>style="display:none;"</cfif>>
		<label for="regEditDeadlineContent" class="col-sm-4 col-form-label-sm font-size-md">
			Expiration Message<br/><i>(displayed after modification deadline has passed)</i>
		</label>
		<div class="col-sm-8">
			<textarea name="regEditDeadlineContent" id="regEditDeadlineContent" class="form-control form-control-sm" rows="3">#arguments.event.getValue('regEditDeadlineContent')#</textarea>
		</div>
	</div>
	<div class="form-group row regEditSetRow" <cfif arguments.event.getValue('regEditAllowed',0) is 0>style="display:none;"</cfif>>
		<label for="regEditRefundContent" class="col-sm-4 col-form-label-sm font-size-md">
			Refund Policy<br/><i>(shown when lowering the paid amount of edited registrations)</i>
		</label>
		<div class="col-sm-8">
			<textarea name="regEditRefundContent" id="regEditRefundContent" class="form-control form-control-sm" rows="3">#arguments.event.getValue('regEditRefundContent')#</textarea>
			<div>If no content is entered here, we'll use the <a href="javascript:getCalendarRefundPolicy();">calendar setting</a>.</div>
		</div>
	</div>
</cfif>

<cfif local.strEvent.qryEventRegMeta.registrationtypeid EQ 1>
	<h5 class="mt-5">Registrant Roster</h5>
	<div class="form-group row mt-2">
		<label class="col-sm-4 col-form-label-sm font-size-md">Registrant Roster</label>
		<div class="col-sm-8">
			<div class="form-check">
				<input type="checkbox" name="enRealTimeRoster" id="enRealTimeRoster" value="1" class="form-check-input" <cfif val(arguments.event.getValue('enableRealTimeRoster'))>checked="checked"</cfif>>
				<label class="form-check-label" for="enRealTimeRoster">Enable Real-Time Registrant Roster (Who's Attending)</label>
			</div>
		</div>
	</div>

	<h5 class="mt-5">Online Video Stream with Time Tracked by Registrant</h5>
	<div class="form-group row mt-2">
		<label class="col-sm-4 col-form-label">Is this an online meeting? *</label>
		<div class="col-sm-8">
			<div class="form-check">
				<input type="radio" name="isOnlineMeeting" id="isOnlineMeeting_0" value="0" class="form-check-input" onClick="clickOnlineMeeting(0);" <cfif arguments.event.getValue('isOnlineMeeting',0) is 0>checked</cfif>>
				<label class="form-check-label" for="isOnlineMeeting_0">No, registrants will attend this live event in person.</label>
			</div>
			<div class="form-check">
				<input type="radio" name="isOnlineMeeting" id="isOnlineMeeting_1" value="1" class="form-check-input" onClick="clickOnlineMeeting(1);" <cfif arguments.event.getValue('isOnlineMeeting',0) is 1>checked</cfif>>
				<label class="form-check-label" for="isOnlineMeeting_1">Yes, registrants will watch a live stream online.</label>
			</div>
		</div>
	</div>

	<div id="divOnlineMeeting" <cfif arguments.event.getValue('isOnlineMeeting',0) is 0>class="d-none"</cfif>>
		<div class="form-group row">
			<label for="onlineEnterStartTime" class="col-sm-4 col-form-label-sm font-size-md">Registrants may login to program between *</label>
			<div class="col-sm-8">
				<div class="row">
					<div class="col-md col-sm-12 pr-md-0">
						<div class="input-group input-group-sm">
							<input type="text" name="onlineEnterStartTime" id="onlineEnterStartTime" value="#dateFormat(arguments.event.getValue('onlineEnterStartTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('onlineEnterStartTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="From" />
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="onlineEnterStartTime"><i class="fa-solid fa-calendar"></i></span>
							</div>
						</div>
					</div>
					<div class="col-md-auto px-md-2 d-flex align-items-center">to</div>
					<div class="col-md col-sm-12 px-md-0">
						<div class="input-group input-group-sm">
							<input type="text" name="onlineEnterEndTime" id="onlineEnterEndTime" value="#dateFormat(arguments.event.getValue('onlineEnterEndTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('onlineEnterEndTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="To" />
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="onlineEnterEndTime"><i class="fa-solid fa-calendar"></i></span>
							</div>
						</div>
					</div>
					<div class="col-md-auto pl-md-2 d-flex align-items-center">#local.regTimeZoneName# Time</div>
				</div>
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-4 col-form-label">
				Embed Stream:<br/>
				<i>Edit Source to embed video (from provider) for automated tracking. Registrants are directed here upon login to event.</i>
				<a href="javascript:previewOM();" class="mt-3">Preview Event</a>
			</div>
			<div class="col-sm-8">
				#application.objWebEditor.embed(objname="onlineEmbedCode", objValue=arguments.event.getValue('onlineEmbedCode',''), tools="VideoEmbedEditor")#
			</div>
		</div>

		<div class="form-group row mt-3">
			<label class="col font-weight-bold">Bypass Embed Stream, Link to Custom Page or Third-Party Site</label>
		</div>
		<div class="form-group row">
			<label for="onlineEmbedOverrideLink" class="col-sm-4 col-form-label-sm font-size-md">Event Redirect URL</label>
			<div class="col-sm-8">
				<input type="text" name="onlineEmbedOverrideLink" id="onlineEmbedOverrideLink" value="#arguments.event.getValue('onlineEmbedOverrideLink','')#" class="form-control form-control-sm" onKeyPress="chkRedURL()" onBlur="chkRedURL()">
				<div id="ev_redurlmsg" class="text-danger font-weight-bold pt-2"></div>
			</div>
		</div>
		<div class="form-group row">
			<label for="justview3" class="col-sm-4 col-form-label-sm font-size-md embedcode text-muted">Embed Code to Track Registrant</label>
			<div class="col-sm-8">
				<input type="text" name="justview3" id="justview3" value="<script type='text/javascript'>$(function() { useEventOnlineMeeting(#arguments.event.getValue('eID')#,''); });</script>" class="form-control form-control-sm" onclick="this.select();" readonly="readonly">
				<div id="ev_embedcodemsg" class="text-danger font-weight-bold pt-2"></div>
			</div>
		</div>
	</div>
<cfelse>
	<input type="hidden" name="isPriceBasedOnActual" value="0">
	<input type="hidden" name="GLAccountID" id="GLAccountID" value="0">
	<input type="hidden" name="isOnlineMeeting" value="0">
	<input type="hidden" name="onlineEmbedCode" value="">
	<input type="hidden" name="onlineEmbedOverrideLink" value="">
	<input type="hidden" name="onlineEnterStartTime" value="">
	<input type="hidden" name="onlineEnterEndTime" value="">
</cfif>
<div class="row mt-3">
	<div class="col">
		<button type="submit" name="btnSaveEventRegDetails" class="btn btn-sm btn-primary btnSaveEventRegDetails">Save Details</button>
	</div>
</div>

</form>
</cfoutput>