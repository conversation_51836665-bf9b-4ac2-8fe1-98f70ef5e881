<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/resourceCustomFields.js#local.assetCachingKey#"></script>
	#local.strProgramFieldsGrid.js#
	#local.strDefaultGLWidget.js#
	<script type="text/javascript">
		function updateProgramSpecificDetails() {
			var updateResult = function(r) {
				$('##btnUpdFldDetails').html('Save Changes').prop('disabled',false);
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##origdefaultGLAccountID').val($('##defaultGLAccountID').val());
					if($('##mccf_btn_addField').is(':disabled'))
						$('##mccf_btn_addField').prop('disabled',false);
				} else {
					mca_showAlert('cp_err_div', 'We are unable to save your changes. Try again.');
				}
			};

			mca_hideAlert('cp_err_div');
			var arrReq = new Array();
			if($('##fieldSectionTitle').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the section title.';
			if($('##defaultGLAccountID').val() == 0 || $.trim($('##defaultGLAccountID').val()).length == 0) arrReq[arrReq.length] = 'Select a default revenue account for custom fields.';

			if(arrReq.length){
				mca_showAlert('cp_err_div', arrReq.join('<br/>'));
				return false;
			}

			$('##btnUpdFldDetails').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Updating...').prop('disabled',true);
			var objParams = { programID:#local.programID#, fieldsSectionTitle:$('##fieldSectionTitle').val(), defaultGLAccountID:$('##defaultGLAccountID').val() };
			TS_AJX('ADMINCONTRIBUTION','updateCustomFieldDetails',objParams,updateResult,updateResult,15000,updateResult);
		}

		<cfif val(arguments.event.getValue('defaultGLAccountID')) is 0>
			$(function() { 
				$('##mccf_btn_addField').prop('disabled',true);
			});
		</cfif>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.customJS#">

<cfoutput>
<div id="mccf_div_custom#local.strProgramFieldsGrid.idExt#">
	<h5>Program-Specific Contribution Fields</h5>
	<div>Fields defined here will be presented to each contributor on this program's intake form.</div>

	<div id="cp_err_div" class="alert alert-danger mb-2 d-none"></div>
	<form name="frmFieldSettings">
		<input type="hidden" name="origdefaultGLAccountID" id="origdefaultGLAccountID" value="#val(arguments.event.getValue('defaultGLAccountID'))#">

		<div class="form-group row mt-2">
			<label for="fieldSectionTitle" class="col-sm-3 col-form-label-sm font-size-md">Section Title *</label>
			<div class="col-sm-9">
				<input type="text" name="fieldSectionTitle" id="fieldSectionTitle" value="#arguments.event.getValue('fieldsSectionTitle')#" class="form-control form-control-sm">
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-12">
				#local.strDefaultGLWidget.html#
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-2 offset-sm-10">
				<button type="button" id="btnUpdFldDetails" name="btnUpdFldDetails" class="btn btn-sm btn-primary" onclick="updateProgramSpecificDetails();">Save Changes</button>
			</div>
		</div>
	</form>
</div>

#local.strProgramFieldsGrid.html#
</cfoutput>