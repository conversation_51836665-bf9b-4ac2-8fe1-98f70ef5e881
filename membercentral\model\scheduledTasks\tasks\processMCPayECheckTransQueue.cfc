<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="590">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="1" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.itemCount = getQueueItemCount()>
		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.success = true>

		<cfif application.MCEnvironment eq "production">
			<cfset local.apiRootURL = "https://payments.usiopay.com/2.0/">
		<cfelse>
			<cfset local.apiRootURL = "https://devpayments.usiopay.com/2.0/">
		</cfif>

		<cfset local.objMCPayECheck = CreateObject("component","model.system.platform.gateways.MCPayECheck")>
		
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_addMCPayECheckTrans_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryMCPayECheckProfile" resultset="1">
			</cfstoredproc>
	
			<cfloop query="local.qryMCPayECheckProfile">
			
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;
	
					DECLARE @grabProcessingStatusID INT;
					EXEC dbo.queue_getStatusIDbyType @queueType='addMCPayECheckTrans', @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;

					select count(itemID) as itemCount
					from dbo.queue_addMCPayECheckTrans
					where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMCPayECheckProfile.itemID#">
					and statusID = @grabProcessingStatusID;
				</cfquery>
				<cfif local.checkItemID.itemCount>

					<cftry>
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='addMCPayECheckTrans', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_addMCPayECheckTrans
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMCPayECheckProfile.itemID#">;
						</cfquery>

						<cfset local.apiRequestBody = '{' & 
							'"MerchantID": "#local.qryMCPayECheckProfile.gatewayMerchantID#",' & 
							'"Login": "#local.qryMCPayECheckProfile.gatewayUsername#",' & 
							'"Password": "#local.qryMCPayECheckProfile.gatewayPassword#",' & 
							'"DateFrom": "#dateTimeFormat(local.qryMCPayECheckProfile.dateFrom,'m/d/yyyy HH:NN:SS')#",' & 
							'"DateTo": "#dateTimeFormat(local.qryMCPayECheckProfile.dateTo,'m/d/yyyy HH:NN:SS')#",' & 
							'"ReturnAccountMask": true,' & 
							'"FilterOptions": "#local.qryMCPayECheckProfile.filterOptions#",' & 
							'"IncludeHPPValues": false,' & 
							'"MaxResults": 0' & <!--- no limit --->
						'}'>

						<cfset local.filteredTransactions = local.objMCPayECheck.callGateway(apiURL="#local.apiRootURL#payments.svc/JSON/GetTransactionList", apiMethod="POST", apiRequestBody=local.apiRequestBody)>
						<cfif local.filteredTransactions.success AND arrayLen(local.filteredTransactions.strAPIResponse.Transactions)>
							<cfset local.arrTransactions = local.filteredTransactions.strAPIResponse.Transactions>

							<cfquery name="local.qryAddMCPayECheckBatchAndTransactions" datasource="#application.dsn.membercentral.dsn#">
								SET XACT_ABORT, NOCOUNT ON;
								BEGIN TRY

								DECLARE @orgID int, @MPProfileID int, @MCPayECheckBatchID int, @BatchNumber varchar(40), @ConfirmationID varchar(30), 
									@transactionID int, @AccountMask varchar(10), @AccountType varchar(10), @PaymentType varchar(40), @Amount decimal(18,2), 
									@submitDate datetime, @returnDate datetime;
								
								SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMCPayECheckProfile.orgID#">;
								SET @MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMCPayECheckProfile.MPProfileID#">;

								<cfloop array="#local.arrTransactions#" index="local.thisTransaction">
									SELECT @MCPayECheckBatchID = NULL, @transactionID = NULL;

									SET @BatchNumber = '#local.thisTransaction.BatchNumber#';
									SET @ConfirmationID = '#local.thisTransaction.Confirmation#';
									SET @AccountMask = '#local.thisTransaction.AccountMask#';
									SET @AccountType = '#local.thisTransaction.AccountType#';
									SET @PaymentType = '#local.thisTransaction.PaymentType#';
									SET @Amount = #val(local.thisTransaction.Amount)#;

									<!--- submitDate is is Microsoft JSON date format /Date(*************-0500)/ --->
									<cfset local.submitJSONDate = local.thisTransaction.submitDate>
									<cfset local.millis = left(reReplace(local.submitJSONDate, "^/Date\((\d+)-.*", "\1"), 13)>
									<cfset local.millisAsLong = inputBaseN(local.millis, 10)>
									<cfset local.submitDateInCT = dateAdd("s", local.millisAsLong / 1000, DateConvert("utc2Local", "January 1 1970 00:00"))>
									SET @submitDate = '#dateTimeFormat(local.submitDateInCT,"m/d/yyyy HH:NN:SS")#';

									<!--- returnDate is in normal datetime format - m/dd/yyyy h:nn:ss tt --->
									<cfif len(local.thisTransaction.ReturnDate) AND isDate(local.thisTransaction.ReturnDate)>
										SET @returnDate = '#local.thisTransaction.ReturnDate#';
									</cfif>
									
									<cfif len(local.thisTransaction.BatchNumber)>
										SELECT @MCPayECheckBatchID = MCPayECheckBatchID
										FROM dbo.tr_MCPayECheckBatches
										WHERE orgID = @orgID
										AND MPProfileID = @MPProfileID
										AND BatchNumber = @BatchNumber;

										IF @MCPayECheckBatchID IS NULL BEGIN
											INSERT INTO dbo.tr_MCPayECheckBatches (orgID, MPProfileID, BatchNumber, depositDate)
											VALUES (@orgID, @MPProfileID, @BatchNumber, CAST(@submitDate AS date));

											SET @MCPayECheckBatchID = SCOPE_IDENTITY();
										END
									</cfif>

									<!--- get transactionID --->
									SELECT @transactionID = tp.transactionID
									FROM dbo.tr_paymentHistory AS ph
									INNER JOIN dbo.tr_transactionPayments AS tp ON tp.orgID = @orgID
										AND tp.historyID = ph.historyID
									WHERE ph.orgID = @orgID
									AND ph.profileID = @MPProfileID
									AND ph.gatewayTransactionID = @ConfirmationID;

									MERGE dbo.tr_MCPayECheckTransactions AS target
									USING (VALUES (@orgID, @MCPayECheckBatchID, @ConfirmationID, @transactionID, @AccountMask, @AccountType, @Amount, @PaymentType, @submitDate, @returnDate)) 
										AS source (orgID, MCPayECheckBatchID, ConfirmationID, transactionID, AccountMask, AccountType, Amount, PaymentType, submitDate, returnDate)
									ON target.orgID = source.orgID AND target.ConfirmationID = source.ConfirmationID

									WHEN MATCHED THEN 
										UPDATE SET 
											target.MCPayECheckBatchID = source.MCPayECheckBatchID,
											target.transactionID = source.transactionID,
											target.AccountMask = source.AccountMask,
											target.AccountType = source.AccountType,
											target.Amount = source.Amount,
											target.PaymentType = source.PaymentType,
											target.submitDate = source.submitDate,
											target.returnDate = source.returnDate

									WHEN NOT MATCHED THEN 
										INSERT (orgID, MCPayECheckBatchID, ConfirmationID, transactionID, AccountMask, AccountType, Amount, PaymentType, submitDate, returnDate)
										VALUES (source.orgID, source.MCPayECheckBatchID, source.ConfirmationID, source.transactionID, source.AccountMask, source.AccountType, 
											source.Amount, source.PaymentType, source.submitDate, source.returnDate);
								</cfloop>
								
								END TRY
								BEGIN CATCH
									IF @@trancount > 0 ROLLBACK TRANSACTION;
									EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
								END CATCH
							</cfquery>
						</cfif>

						<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
							DELETE FROM dbo.queue_addMCPayECheckTrans
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMCPayECheckProfile.itemID#">;
						</cfquery>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfif>
			</cfloop>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_addMCPayECheckTrans;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>
	
</cfcomponent>