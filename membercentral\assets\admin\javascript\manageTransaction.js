function showEmailRefundStatementMergeInstr(u) {
	$('#divComposeMessageForm,#divPreviewMessageForm').addClass('d-none');
	$('#divMergeCodeScreen').removeClass('d-none');
	$('#divMergeCodeScreen div#divMergeCodeScreenContent').html(mca_getLoadingHTML('Loading the merge codes...')).load(u);
}
function showEmailRefundStatementComposeMessageForm() {
	$('#divMergeCodeScreen div#divMergeCodeScreenContent').html('');
	$('#divMergeCodeScreen,#divPreviewMessageForm').addClass('d-none');
	$('#divComposeMessageForm').removeClass('d-none');
}
function hideEmailRefundStatementAlerts() {
	mca_hideAlert('err_email_compose');
	mca_hideAlert('err_email_preview');
	mca_hideAlert('err_email_templatesave');
}
function loadEmailRefundStatementEmailTemplateContent() {
	hideEmailRefundStatementAlerts();
	$('#divEmailFields').addClass('d-none');

	var result = function(r) {
		$('#loadingTemp').addClass('d-none');
		if (r.success && r.success.toLowerCase() == 'true'){
			try { CKEDITOR.instances['templateContent'].resize('100%','100%'); } catch(e) {};
			CKEDITOR.instances['templateContent'].setData(r.emailcontent);
			$('#emailSubject').val(r.subjectline);
			$('#emailReplyTo').val(r.emailfrom);
			$('#emailFromName').val(r.emailfromname);
			$('#divEmailFields').removeClass('d-none');
		} else {
			mca_showAlert('err_email_compose', 'An error occurred while loading the template.');
		}
	};

	try { CKEDITOR.instances['templateContent'].setData(''); } catch(e) {};
	$('#emailSubject, #emailReplyTo, #emailFromName').val('');
	if ($('#fEmailTemplateID').val() != 0) {
		$('#loadingTemp').removeClass('d-none');
		var objParams = { emailTemplateID:$('#fEmailTemplateID').val() };
		TS_AJX('MASSEMAIL','getEmailTemplateContent',objParams,result,result,10000,result);
	}
}
function editEmailRefundStatementRefundStatementMessage() {
	hideEmailRefundStatementAlerts();
	$('#btnEmailRefundStatement').prop('disabled',false);
	$('#divComposeMessageForm').removeClass('d-none');
	$('#divPreviewMessageForm, #divTestEmail').addClass('d-none');
	$('#divEmailDisp, #spFrom, #spSubject').html('');
	$('#spTestBtn').addClass('d-none');
}
function showEmailRefundStatementBuildEmailOptions(val) {
	hideEmailRefundStatementAlerts();
	$('#emailFromName, #emailReplyTo, #emailSubject').val('');
	try { CKEDITOR.instances['templateContent'].setData(''); } catch(e) {};
	$('#divBuildEmailTemplate, #divEmailFields').addClass('d-none');

	if (val == 'new') {
		$("#fEmailTemplateID").val(null).trigger('change');
		if (mc_numTemplates > 0) $('#divEmailTemplateSel').addClass('d-none');
		try { CKEDITOR.instances['templateContent'].resize('100%','100%'); } catch(e) {};
		$('#divEmailFields, #divBuildEmailTemplate').removeClass('d-none');
		$('#emailFromName').val(mc_orgname);
	} else {
		$('#divEmailTemplateSel, #divBuildEmailTemplate').removeClass('d-none');
	}
}
function showCustomEmailRefundStatementTemplates(r) {
	var arrReq = [];
	var emailList = $('#recipientEmailList').val().trim().split(';');
	
	if(emailList.length == 0) arrReq[arrReq.length] = 'Enter the recipient e-mail address.';
	else{
		var valid = true;
		$.each(emailList, function(index, email) {
			email = email.trim();
			var emailRegEx = new RegExp(r,"i");

			if (!(emailRegEx.test(email))) {
				valid = false;
				return false;
			}
		});
		if(!valid) arrReq[arrReq.length] = 'Some email addresses are invalid.';
	}
	
	if(arrReq.length){
		mca_showAlert('err_email_preview', arrReq.join('<br/>'), true);
		return false;
	}


	if (mc_numTemplates > 0) {
		if ($('select#fEmailTemplateID').length && $('#fEmailTemplateID').val() != '') {
			$('b#selTemplateOption').html($('#fEmailTemplateID option:selected').text());
			$('#selTemplateRow').removeClass('d-none');
		}
	}

	$('#divPreviewMessageForm').addClass('d-none');
	$('#divTemplateSaveOptions').removeClass('d-none');
}
function hideEmailRefundStatementNewTemplateSaveDetails() {
	hideEmailRefundStatementAlerts();
	$('#templateName').val('');
	$('#selCategory').val('');
	$('#newTemplateDetailsRow').addClass('d-none');
}
function showEmailRefundStatementNewTemplateSaveDetails() {
	hideEmailRefundStatementAlerts();
	$('#newTemplateDetailsRow').removeClass('d-none');
	changeEmailRefundStatementTemplateCategoryOptions();
}
function validateMassRefundStatementEmailForm() {
	$('#btnSubmit').text('Please Wait..').attr('disabled',true);
	var arrReq = new Array();
	if ($('input[name="saveTemplateOption"]:checked').val() == 1 && $('#templateName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the template name.';
	if ($('input[name="massEmailScheduling"]:checked').val() == 'later' && $('#emailDateScheduled').val().length == 0) arrReq[arrReq.length] = 'Enter the scheduled sending date.';
	
	if(arrReq.length){
		$('#btnSubmit').text('Send E-mails').attr('disabled',false);
		mca_showAlert('err_email_preview', arrReq.join('<br/>'), true);
		return false;
	}
	return true;
}
function changeEmailRefundStatementTemplateCategoryOptions() {
	if($('#selCategory').val() == 0)
		$('#divNewCategory').removeClass('d-none');
	else {
		$('#newCategoryName').val('');
		$('#divNewCategory').addClass('d-none');
	}
}
function previewCustomEmailRefundStatementMessage(r) {
	hideEmailRefundStatementAlerts();
	$('#btnEmailRefundStatement').prop('disabled',true);

	var arrReq = new Array();
	var emailRegEx = new RegExp(r,"i");
	if($('#emailFromName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail from.';
	else if($('#emailFromName').val().trim().length > 0 && emailRegEx.test($('#emailFromName').val())) arrReq[arrReq.length] = 'E-mail From Name cannot contain an email address.';
	
	if($('#emailReplyTo').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail reply-to address.';
	else if ($('#emailReplyTo').val().trim().length > 0 && !(emailRegEx.test($('#emailReplyTo').val()))) arrReq[arrReq.length] = 'Enter a valid e-mail reply-to address.';
	
	if($('#emailSubject').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail subject.';
	if($.trim(CKEDITOR.instances['templateContent'].getData()).length == 0) arrReq[arrReq.length] = 'Enter the e-mail content.';

	if(arrReq.length){
		$('#btnEmailRefundStatement').prop('disabled',false);
		mca_showAlert('err_email_compose', arrReq.join('<br/>'), true);
		return false;
	}

	showCustomEmailRefundStatementMessage(transactionID);
}
function showCustomEmailRefundStatementMessage(id) {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#divPreviewEmailMessageLoading').addClass('d-none');
			$('#divTestEmail, #spTestBtn').removeClass('d-none');
			$('#divEmailDisp').html(r.templatedisp);
			$('#spFrom').html($('#emailReplyTo').val() + ' (' + $('#emailFromName').val() + ')');
			$('#spSubject').html(decodeURIComponent(r.subjectline));
		} else {
			alert('Unable to generate message preview.');
		}
	};

	$('#divComposeMessageForm').addClass('d-none');
	$('#divPreviewMessageForm, #divPreviewEmailMessageLoading').removeClass('d-none');
	$('#spTestBtn').addClass('d-none');
	$('#divTestPreviewMessage').html('').addClass('d-none');

	var templateContent = CKEDITOR.instances['templateContent'].getData();
	var objParams = {transactionID:id, templateContent:encodeURIComponent(templateContent), 
						subjectLine:encodeURIComponent($('#emailSubject').val()) };
	TS_AJX('ADMTRANSACTIONS','getPreviewCustomEmailRefundStatementMessage',objParams,result,result,20000,result);
}
function sendCustomTestRefundStatementEmail() {
	var sendTestResult = function(r) {
		$('#btnTestTemplate').attr('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') { 
			$('#divTestPreviewMessage').html('<i class="fa-regular fa-thumbs-up fa-lg"></i> Test e-mail sent successfully');
		} else {
			$('#divTestPreviewMessage').html('Error sending test e-mail').addClass('class','text-danger font-weight-bold');
		}
	};

	$('#btnTestTemplate').attr('disabled',true);
	$('#divTestPreviewMessage').removeClass('text-danger font-weight-bold').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Sending test e-mail...').removeClass('d-none');

	var templateContent = CKEDITOR.instances['templateContent'].getData();
	var objParams = { transactionID:transactionID, memberID:$('#memberID').val(), templateContent:encodeURIComponent(templateContent), 
						subjectLine:encodeURIComponent($('#emailSubject').val()), emailFromName:$('#emailFromName').val(), emailfrom:$('#emailReplyTo').val(), refundStatementDesc:$('#refundDesc').val() };
	TS_AJX('ADMTRANSACTIONS','sendCustomRefundStatementTestEmailMessage',objParams,sendTestResult,sendTestResult,40000,sendTestResult);
}
function validateRefundStatementEmailForm() {
	hideEmailRefundStatementAlerts();
	$('#btnSubmit').text('Please Wait..').attr('disabled',true);
	
	var arrReq = new Array();
	if ($('input[name="saveTemplateOption"]:checked').val() == 1) {
		if ($('#templateName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the template name.';
		if (Number($('#selCategory').val()) == 0 && $('#newCategoryName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the new template category name.';
	}
	if ($('input[name="massEmailScheduling"]:checked').val() == 'later' && $('#emailDateScheduled').val().length == 0) arrReq[arrReq.length] = 'Enter the scheduled sending date.';
	
	if(arrReq.length){
		$('#btnSubmit').text('Send E-mails').attr('disabled',false);
		mca_showAlert('err_email_templatesave', arrReq.join('<br/>'), true);
		return false;
	}
	return true;
}